export class ApiKeysSerializer {
  static serializeApiKey(dbApiKey: {
    uuid: string;
    name: string;
    key: string;
    created_at: Date;
    is_active: boolean;
  }) {
    return {
      uuid: dbApiKey.uuid,
      name: dbApi<PERSON>ey.name,
      key: dbApi<PERSON>ey.key,
      created_at: dbApi<PERSON>ey.created_at,
      is_active: dbApiKey.is_active,
    };
  }

  static serializeApiKeyList(
    dbApiKeys: Array<{
      uuid: string;
      name: string;
      key: string;
      created_at: Date;
      is_active: boolean;
    }>,
  ) {
    return dbApiKeys.map(this.serializeApiKey);
  }

  static serializeCreateApiKeyResponse(dbApiKey: {
    uuid: string;
    name: string;
    key: string;
    user_id: string;
    created_at: Date;
  }) {
    return {
      uuid: dbApiKey.uuid,
      name: dbApi<PERSON>ey.name,
      key: dbApiKey.key,
      created_at: dbApiKey.created_at,
    };
  }
}
