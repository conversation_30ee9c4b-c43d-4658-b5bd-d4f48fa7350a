{"name": "backend", "version": "1.0.0", "type": "module", "description": "Bare minimum Express 5.1 backend with TypeScript", "main": "dist/index.js", "scripts": {"build": "tsup", "build:dev": "tsup --sourcemap", "build:watch": "tsup --watch", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "clean": "rm -rf dist", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint . --fix", "db:generate": "dotenv -e ../../.env -- drizzle-kit generate", "db:generate:dev": "dotenv -e ../../.env.local -- drizzle-kit generate", "db:migrate": "dotenv -e ../../.env -- drizzle-kit migrate", "db:migrate:dev": "dotenv -e ../../.env.local -- drizzle-kit migrate"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.3", "@repo/trpc": "workspace:*", "@repo/zod-types": "workspace:*", "@trpc/server": "^11.4.1", "basic-auth": "^2.0.1", "better-auth": "^1.2.9", "cors": "^2.8.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "express": "^5.1.0", "helmet": "^8.1.0", "nanoid": "^5.1.5", "pg": "^8.16.0", "shell-quote": "^1.8.3", "spawn-rx": "^5.1.2", "zod": "^3.25.64"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@types/basic-auth": "^1.1.8", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/node": "^20.0.0", "@types/pg": "^8.15.4", "@types/shell-quote": "^1.7.5", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.1", "eslint": "^9.28.0", "tsup": "^8.5.0", "tsx": "^4.20.3", "typescript": "^5.0.0"}, "keywords": [], "author": "", "license": "ISC"}