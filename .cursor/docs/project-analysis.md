# 项目梳理文档

## 1. 《项目文档整理规范》（草案）

这份文档旨在为项目提供清晰、全面的技术参考，方便新成员快速上手，并指导未来的开发和维护工作。

### 1.1. 文档概述 (Overview)

#### 1.1.1. 项目简介
本项目是一个专门为 **模型上下文协议 (Model Context Protocol, MCP)** 设计的后端管理与代理服务。其核心目标是提供一个统一的平台来注册、管理和连接不同类型的 MCP 服务器（如本地 Stdio、远程 SSE 服务等），并通过命名空间（Namespace）和端点（Endpoint）机制，将这些服务安全、高效地暴露给外部消费者。

它既为管理后台提供了一套完整的 tRPC API，也为最终用户提供了高性能的请求代理服务。

#### 1.1.2. 核心功能
*   **MCP 服务器管理**: 支持对多种类型的 MCP 服务器（Stdio, SSE, Streamable HTTP）进行增删改查。
*   **命名空间 (Namespace) 管理**: 允许将多个 MCP 服务器及其提供的工具（Tools）逻辑上聚合到一个命名空间中，便于统一管理和授权。
*   **端点 (Endpoint) 管理**: 可以创建公开的路由端点，并将其映射到指定的命名空间，实现服务的对外暴露。
*   **灵活的认证机制**:
    *   **用户认证**: 集成 `better-auth` 为管理后台提供完整的用户会话管理。
    *   **API 密钥认证**: 支持为公开端点配置 API 密钥认证，保障服务安全。
*   **类型安全的 API**: 使用 tRPC 为前端提供类型安全的 API，提升开发效率和代码健壮性。
*   **高性能请求代理**: 基于 Express 构建，为 MCP 请求提供低延迟的代理和流式传输能力。

#### 1.1.3. 技术栈
*   **语言**: TypeScript
*   **运行时**: Node.js
*   **Web 框架**: Express 5.1
*   **API 层**: tRPC
*   **数据库**: PostgreSQL
*   **ORM**: Drizzle ORM
*   **构建工具**: tsup
*   **认证库**: better-auth, basic-auth

### 1.2. 架构设计 (Architecture)

#### 1.2.1. 系统架构图
*请参见本文档第二部分自动生成的系统架构图。* 该图展示了系统的主要组件，包括外部请求方、Express 后端服务及其内部路由，以及与数据库和外部 MCP 服务器的交互关系。

#### 1.2.2. 核心概念
本项目的架构围绕以下几个核心概念构建，理解它们是理解整个系统的关键。

*   **MCP 服务器 (`mcpServersTable`)**:
    *   **定义**: MCP 服务器是实现了模型上下文协议（MCP）的服务单元。它可以是一个本地的可执行文件（通过 `STDIO` 通信），也可以是一个远程的 HTTP 服务（通过 `SSE` 或 `Streamable_HTTP` 通信）。
    *   **作用**: 每个 MCP 服务器可以提供一个或多个“工具”（Tools），这些工具是模型可以调用的具体功能。
    *   **管理**: 系统允许管理员注册、配置（如启动命令、URL、环境变量）和管理这些服务器。

*   **工具 (`toolsTable`)**:
    *   **定义**: 工具是 MCP 服务器提供的具体能力单元，例如代码生成、文件操作等。每个工具都有一个明确的输入/输出模式（Schema）。
    *   **来源**: 工具由其所属的 MCP 服务器在连接时动态注册到本系统中。本系统负责存储其 Schema 和元数据。

*   **命名空间 (`namespacesTable`)**:
    *   **定义**: 命名空间是一个逻辑**集合**，用于组织和管理一组相关的 MCP 服务器及其工具。
    *   **作用**: 它是权限控制和服务聚合的核心。通过将服务器和工具添加到命名空间中，可以实现：
        1.  **服务聚合**: 将来自不同 MCP 服务器的工具组合成一个统一的服务集。
        2.  **状态管理**: 可以独立启用或禁用命名空间下的某个服务器或特定工具，实现精细化的服务治理。
    *   **关系**: 通过 `namespaceServerMappingsTable` 和 `namespaceToolMappingsTable` 这两个中间表，实现了命名空间与服务器、工具之间的多对多关系。

*   **端点 (`endpointsTable`)**:
    *   **定义**: 端点是一个对外暴露的、可通过 HTTP 访问的 URL 路径（例如 `/metamcp/my-awesome-agent`）。
    *   **作用**: 它是将内部服务（命名空间）安全地暴露给外部消费者的**唯一入口**。每个端点都必须映射到一个且仅一个命名空间。
    *   **认证**: 端点可以配置独立的认证方式，目前支持 API 密钥认证，从而保护后端服务不被未授权的访问。

*   **数据流总结**:
    1.  **管理流 (tRPC)**: 管理员通过前端 UI 调用后端的 tRPC 接口 (`/trpc/frontend`)，对服务器、命名空间、端点等资源进行 CRUD 操作。这些操作最终会写入 PostgreSQL 数据库。
    2.  **服务流 (MCP Proxy)**: 外部 API 消费者携带有效的 API 密钥，请求一个公开的端点 URL (`/metamcp/...`)。
    3.  请求到达后，系统通过端点找到其映射的命名空间。
    4.  系统根据命名空间中配置的、且状态为“激活”的 MCP 服务器和工具列表，构造一个上下文。
    5.  最终，请求被代理到相应的后端 MCP 服务器，完成与模型的交互。

### 1.3. 模块详解 (Modules)

#### 1.3.1. 入口与路由 (`src/index.ts`)
应用的入口点是 [`src/index.ts`](src/index.ts:1)。它负责初始化 Express 应用实例、加载全局中间件并挂载核心路由模块。

**启动流程与中间件:**
1.  **初始化 Express**: 创建 `app` 实例。
2.  **请求日志**: 一个简单的中间件，用于在控制台打印所有收到的请求（方法和路径）。
3.  **条件 JSON 解析**: 这是一个关键的中间件。它会判断请求路径，如果请求是发往 MCP 代理 (`/mcp-proxy/`) 或公共端点 (`/metamcp/`) 的，它会**跳过** `express.json()` 解析。这是为了保证这些路径可以处理原始的、非 JSON 格式的请求体，例如流式数据。对于所有其他路由（如 tRPC），则正常使用 JSON 解析。
4.  **Auth 路由处理器**: 一个特殊的中间件，用于拦截所有发往 `/api/auth` 的请求。它会将 Express 的 `req` 对象转换为标准的 Web `Request` 对象，然后直接调用 `better-auth` 的 `auth.handler` 进行处理，并将返回的 `Response` 转换回 Express 的 `res` 对象。这是一种在 Express 中直接集成遵循 Web 标准的认证库的巧妙方式。
5.  **路由挂载**: 按照特定顺序挂载各个路由模块：
    *   `app.use("/metamcp", publicEndpointsRouter)`: 挂载公共端点路由。
    *   `app.use("/mcp-proxy", mcpProxyRouter)`: 挂载 MCP 代理路由。
    *   `app.use("/trpc", trpcRouter)`: 挂载 tRPC 路由。
6.  **启动监听**: 应用在 `12009` 端口启动服务。

#### 1.3.2. tRPC 接口 (`src/routers/trpc.ts`)
该文件定义了所有供前端管理后台使用的 API。

**结构与实现:**
*   **统一路由 (`appRouter`)**: 使用从 `@repo/trpc` 导入的 `createAppRouter` 函数创建了一个根路由 `appRouter`。所有的子路由都被组织在 `frontend` 命名空间下。
*   **模块化实现**: `appRouter` 聚合了来自 `src/trpc/*.impl.ts` 文件中的具体实现。每个 `*.impl.ts` 文件对应一个核心资源（如 `mcpServers`, `namespaces`, `apiKeys` 等），使其结构清晰且易于维护。
*   **Express 适配器**: 使用 `@trpc/server/adapters/express` 的 `createExpressMiddleware` 将 `appRouter` 包装成一个标准的 Express 中间件。
*   **端点**: tRPC 服务最终被挂载在 `/trpc/frontend` 路径下。
*   **上下文创建 (`createContext`)**: 中间件在处理每个请求时，都会调用从 [`src/trpc.ts`](src/trpc.ts:1) 导入的 `createContext` 函数。此函数负责从请求的 Cookie 中解析 `better-auth` 的会话信息，并将用户信息（`user` 和 `session`）注入到 tRPC 的上下文中，供后续的 `protectedProcedure` 使用。
*   **安全性**: 路由应用了 `helmet` 和 `cors` 中间件，以增强安全性。`cors` 被配置为仅接受来自环境变量 `APP_URL` 指定的前端域名的跨域请求。

#### 1.3.3. MCP 代理 (`src/lib/mcp-proxy.ts`, `src/routers/mcp-proxy/`)
这是系统的核心服务，负责处理所有到 MCP 服务器的代理请求。

*   **实现**: 代理逻辑主要在 [`src/lib/mcp-proxy.ts`](src/lib/mcp-proxy.ts:1) 和 [`src/lib/metamcp/metamcp-proxy.ts`](src/lib/metamcp/metamcp-proxy.ts:1) 中实现。它接收一个请求，然后根据请求的参数（如端点名称）从数据库中查询相应的命名空间、服务器和工具配置。
*   **动态上下文**: 它会动态构建一个 `MetaMCP` 实例，这个实例包含了从数据库加载的所有激活的服务器和工具。
*   **请求转发**: 构建完成后，它将原始请求直接转发给 `MetaMCP` 实例的 `handle` 方法进行处理。`MetaMCP` 内部会根据协议（STDIO/SSE等）与下游的真实 MCP 服务器进行通信，并将结果流式返回给客户端。
*   **路由**:
    *   [`src/routers/public-metamcp/`](src/routers/public-metamcp/) 目录下的路由处理的是通过“端点”暴露的公共代理请求。
    *   [`src/routers/mcp-proxy/`](src/routers/mcp-proxy/) 目录下的路由可能用于内部或更直接的代理场景。

#### 1.3.4. 认证与授权 (`src/auth.ts`, `src/trpc.ts`)
系统包含两层认证机制：

*   **用户会话认证 (for tRPC)**:
    *   **实现**: 通过 `better-auth` 库实现，配置文件为 [`src/auth.ts`](src/auth.ts:1)。它使用 `DrizzleAdapter` 将用户、会话等信息存储在 PostgreSQL 数据库中。
    *   **流程**: 用户通过前端登录后，`better-auth` 会生成一个会话，并通过 Cookie 存储在浏览器中。
    *   **上下文集成**: 在 [`src/trpc.ts`](src/trpc.ts:1) 的 `createContext` 函数中，系统会从请求的 Cookie 中解析会话，并将用户信息注入到 tRPC 上下文中。
    *   **访问控制**: tRPC 的 `protectedProcedure` 是一个中间件，它检查上下文中是否存在用户信息。如果不存在，则拒绝访问并抛出 `UNAUTHORIZED` 错误，从而保护需要登录才能访问的 API。

*   **API 密钥认证 (for Endpoints)**:
    *   **实现**: 在代理路由的中间件中实现（例如 [`src/middleware/better-auth-mcp.middleware.ts`](src/middleware/better-auth-mcp.middleware.ts:1)）。
    *   **流程**: 当外部服务请求一个需要 API 密钥的“端点”时，该中间件会从请求头（`Authorization: Bearer <key>`）或查询参数中提取密钥。
    *   **验证**: 中间件会查询 `apiKeysTable` 数据库表，验证密钥是否存在且处于激活状态。如果验证失败，请求将被拒绝。

#### 1.3.5. 数据库 (`src/db/`)
数据库是系统的核心状态存储。

*   **数据模型 (Schema)**:
    *   定义在 [`src/db/schema.ts`](src/db/schema.ts:1) 中，使用 Drizzle ORM 的 `pgTable` 函数定义了所有的数据表结构、字段类型、约束和索引。
    *   *关于表关系的详细可视化，请参见本文档第二部分的数据库核心 ER 图。*

*   **数据仓库 (Repositories)**:
    *   **模式**: 项目采用了仓储模式（Repository Pattern），将数据访问逻辑封装在 `src/db/repositories/*.repo.ts` 文件中。
    *   **目的**: 这种模式的主要优点是**关注点分离**。tRPC 的实现（`impl.ts` 文件）不直接与 Drizzle ORM 或数据库交互，而是通过调用仓储方法来执行操作。这使得业务逻辑与数据访问逻辑解耦，代码更清晰，也更容易进行单元测试（可以通过模拟仓储层来实现）。
    *   **示例**: [`src/db/repositories/mcp-servers.repo.ts`](src/db/repositories/mcp-servers.repo.ts:1) 封装了所有与 `mcpServersTable` 相关的数据库操作，如 `createServer`, `getServerByName` 等。

### 1.4. 开发与部署 (Development & Deployment)

#### 1.4.1. 环境设置
1.  **先决条件**:
    *   Node.js (建议使用 v20 或更高版本)
    *   pnpm (或 npm / yarn)
    *   一个正在运行的 PostgreSQL 数据库实例。

2.  **安装依赖**:
    ```bash
    pnpm install
    ```

3.  **配置环境变量**:
    *   项目使用 `.env` 文件来管理环境变量。在项目根目录（`metamcp-main 3/`）下，复制 `.env.example` 文件为 `.env.local`。
    *   根据你的本地环境，填写 `.env.local` 中的配置，尤其是 `DATABASE_URL`，使其指向你的本地 PostgreSQL 数据库。
    *   `db:migrate:dev` 和 `db:generate:dev` 命令会优先使用 `.env.local` 文件。

4.  **数据库迁移**:
    *   在首次启动应用前，你需要运行数据库迁移来创建所有的表结构。
    ```bash
    pnpm run db:migrate:dev
    ```

#### 1.4.2. 常用命令
所有命令都定义在 [`package.json`](package.json:1) 的 `scripts` 部分。

*   `pnpm run dev`:
    *   使用 `tsx` 启动开发服务器，支持热重载。这是本地开发中最常用的命令。

*   `pnpm run build`:
    *   使用 `tsup` 将 TypeScript 源码编译为 JavaScript，输出到 `dist/` 目录。这是生产部署前的必要步骤。

*   `pnpm run start`:
    *   使用 `node` 直接运行 `dist/` 目录中已编译好的代码。用于在生产环境中启动服务。

*   `pnpm run lint` / `pnpm run lint:fix`:
    *   使用 ESLint 检查代码规范，`--fix` 参数会尝试自动修复问题。

*   `pnpm run db:generate:dev`:
    *   当你在 [`src/db/schema.ts`](src/db/schema.ts:1) 中修改了表结构后，运行此命令。Drizzle Kit 会自动生成对应的 SQL 迁移文件，存放在 `drizzle/` 目录下。

*   `pnpm run db:migrate:dev`:
    *   执行 `drizzle/` 目录中尚未执行的迁移文件，将变更应用到开发数据库。

#### 1.4.3. 部署指南
1.  **构建项目**: 在服务器上，首先拉取最新代码，然后执行构建命令。
    ```bash
    pnpm install
    pnpm run build
    ```

2.  **配置环境变量**:
    *   在生产服务器上，建议直接设置系统环境变量，或者创建一个 `.env` 文件（位于 `metamcp-main 3/` 目录下）。
    *   确保所有必要的变量（如 `DATABASE_URL`, `APP_URL`, `AUTH_SECRET` 等）都已正确配置。

3.  **数据库迁移**:
    *   在启动应用前，务必执行生产数据库的迁移。
    ```bash
    pnpm run db:migrate
    ```

4.  **启动应用**:
    *   建议使用一个进程管理工具（如 PM2）来启动和守护应用，以确保其稳定运行和自动重启。
    ```bash
    # 安装 PM2 (如果尚未安装)
    pnpm install -g pm2

    # 使用 PM2 启动应用
    pm2 start dist/index.js --name "mcp-backend"
    ```

---

## 2. 项目梳理执行计划

### 第一阶段：生成整体架构图和数据模型图

*   **系统架构图**:
    ```mermaid
    graph TD
        subgraph "外部请求"
            User[👨‍💻 用户/客户端]
            API_Consumer[🤖 API 消费者]
        end

        subgraph "后端服务 (Express App)"
            A[入口: src/index.ts] --> B{中间件};
            B --> C1[tRPC 路由 /trpc];
            B --> C2[MCP 代理 /mcp-proxy];
            B --> C3[公共端点 /metamcp];
            B --> C4[认证路由 /api/auth];

            C1 -- tRPC调用 --> D1[tRPC 实现];
            D1 -- 读写 --> DB[(🐘 PostgreSQL 数据库)];

            C2 -- 代理 --> E[MCP 服务器 (Stdio/SSE)];
            C3 -- 代理 --> E;
            C4 -- 认证 --> DB;
        end

        subgraph "外部依赖"
             E
        end

        User -- 访问管理后台 --> C1;
        API_Consumer -- 调用服务 --> C3;
    ```

*   **数据库核心 ER 图**:
    ```mermaid
    erDiagram
        usersTable {
            string id PK
            string name
            string email
        }

        apiKeysTable {
            uuid uuid PK
            string key
            string user_id FK
        }

        mcpServersTable {
            uuid uuid PK
            string name
            enum type
            string command
            string url
        }

        toolsTable {
            uuid uuid PK
            string name
            json toolSchema
            uuid mcp_server_uuid FK
        }

        namespacesTable {
            uuid uuid PK
            string name
        }

        endpointsTable {
            uuid uuid PK
            string name
            uuid namespace_uuid FK
        }

        namespaceServerMappingsTable {
            uuid namespace_uuid PK,FK
            uuid mcp_server_uuid PK,FK
        }

        namespaceToolMappingsTable {
            uuid namespace_uuid PK,FK
            uuid tool_uuid PK,FK
        }

        usersTable ||--o{ apiKeysTable : "拥有"
        mcpServersTable ||--o{ toolsTable : "提供"
        namespacesTable ||--|{ namespaceServerMappingsTable : "包含"
        mcpServersTable ||--|{ namespaceServerMappingsTable : "属于"
        namespacesTable ||--|{ namespaceToolMappingsTable : "包含"
        toolsTable ||--|{ namespaceToolMappingsTable : "属于"
        namespacesTable ||--o{ endpointsTable : "映射到"
    ```

### 第二阶段：撰写详细文档

根据上述规范的章节结构，结合代码分析，逐一填充每个部分的内容。

### 第三阶段：交付

将最终生成的 Markdown 文档写入到项目根目录下的 `.cursor/docs/project-analysis.md` 文件中。