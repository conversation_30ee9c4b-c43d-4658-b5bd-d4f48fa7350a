---
description: 
globs: *,*.*
alwaysApply: false
---
# Metatool Dart 项目结构

本文档概述了 `metatool_dart` 项目的目录和文件结构。

主要应用程序代码位于 `metatool_dart/lib/` 中。项目配置在 [`pubspec.yaml`](mdc:metatool_dart/pubspec.yaml) 文件中定义。

## 数据层 (`metatool_dart/lib/src/data`)

`data` 目录包含与数据持久化和管理相关的所有组件。

### `database`

-   **描述**: 包含使用 Drift 的数据库设置和配置。
-   **关键文件**:
    -   [`app_database.dart`](mdc:metatool_dart/lib/src/data/database/app_database.dart): 定义主数据库类、表和 DAO。
    -   `tables/`: 用于存放表结构定义的目录。每个文件对应一个数据库表。

### `models`

-   **描述**: 定义整个应用程序中使用的数据模型。这些模型代表从数据库中检索的数据结构。
-   **约定**: 每个模型文件，例如 [`account_model.dart`](mdc:metatool_dart/lib/src/data/models/account_model.dart)，都有一个相应的生成文件 (`.g.dart`) 用于序列化/反序列化逻辑。

### `repositories`

-   **描述**: 包含对数据源进行抽象的 Repository 类。它们为应用程序的其余部分提供了清晰的数据访问 API。
-   **示例**: [`accounts_repository.dart`](mdc:metatool_dart/lib/src/data/repositories/accounts_repository.dart) 处理与账户相关的所有数据库操作。

### `enums`

-   **描述**: 存放应用中使用的自定义枚举类型。

### `exceptions`

-   **描述**: 定义用于处理特定错误条件的自定义异常类。

### `utils`

-   **描述**: 跨数据层使用的工具函数和类的集合，例如数据转换器。
