---
description: 
globs: *,*.*
alwaysApply: false
---
# TypeScript to Dart Porting Guide

This document outlines the requirements for porting the existing TypeScript backend to a new Dart implementation.

## Core Requirements

1.  **Target Directory**: All new Dart code must be placed in the `metatool_dart/` directory.
2.  **Project Structure**: The Dart project's file structure should mirror the original TypeScript project. For each `.ts` file, a corresponding `.dart` file should be created.
3.  **Database**: Use a pure Dart SQLite library for all database operations. Avoid libraries that rely heavily on code generation or annotations for defining schemas and queries.
4.  **MCP Implementation**: All Model Context Protocol (MCP) functionality must be implemented using the `mcp_dart` package, specifically version `^0.5.2`. For detailed SDK documentation, query the `leehack/mcp_dart` repository using deepwiki tool.
5.  **Coding Style**: The resulting Dart code must be idiomatic and follow standard Dart conventions and best practices.
6.  **API Reference**: The existing API is documented in [MCP-Dart-API参考手册.md](mdc:MCP-Dart-API参考手册.md). This document should be consulted during the porting process.
