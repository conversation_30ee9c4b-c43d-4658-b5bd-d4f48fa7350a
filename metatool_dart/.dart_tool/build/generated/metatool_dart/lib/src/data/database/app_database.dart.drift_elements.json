{"valid_import": true, "imports": [{"uri": "package:drift/drift.dart", "transitive": false}, {"uri": "package:drift/native.dart", "transitive": false}, {"uri": "package:path_provider/path_provider.dart", "transitive": false}, {"uri": "package:path/path.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/tools.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/users.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/verifications.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/namespace_server_mappings.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/config.dart", "transitive": false}], "elements": [{"kind": "database", "name": "AppDatabase", "dart_name": "AppDatabase"}]}