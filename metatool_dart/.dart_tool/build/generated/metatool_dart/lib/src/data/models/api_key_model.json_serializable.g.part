// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApiKeyModel _$ApiKeyModelFromJson(Map<String, dynamic> json) => ApiKeyModel(
      uuid: json['uuid'] as String,
      name: json['name'] as String,
      key: json['key'] as String,
      userId: json['userId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$ApiKeyModelToJson(ApiKeyModel instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'name': instance.name,
      'key': instance.key,
      'userId': instance.userId,
      'createdAt': instance.createdAt.toIso8601String(),
      'isActive': instance.isActive,
    };
