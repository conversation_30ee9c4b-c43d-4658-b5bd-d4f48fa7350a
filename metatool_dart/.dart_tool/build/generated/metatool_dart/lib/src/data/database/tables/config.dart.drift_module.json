{"elements": {"config": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/config.dart", "name": "config"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/config.dart", "offset": 72, "name": "Config"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/config.dart", "offset": 112, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "value", "nameInDart": "value", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/config.dart", "offset": 145, "name": "value"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/config.dart", "offset": 181, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/config.dart", "offset": 238, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/config.dart", "offset": 318, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["id"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Config", "row_class_name": "ConfigEntry", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart"]}