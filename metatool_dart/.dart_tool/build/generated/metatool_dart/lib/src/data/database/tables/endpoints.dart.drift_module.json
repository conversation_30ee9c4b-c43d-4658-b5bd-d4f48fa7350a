{"elements": {"endpoints": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "name": "endpoints"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 95, "name": "Endpoints"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "name": "namespaces"}], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "uuid", "nameInDart": "uuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 138, "name": "uuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 36, "max_length": 36}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 202, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 246, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "namespace_uuid", "nameInDart": "namespaceUuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 299, "name": "namespaceUuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "name": "namespaces"}, "name": "uuid"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "enable_api_key_auth", "nameInDart": "enableApiKeyAuth", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 402, "name": "enableApiKeyAuth"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(true)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "use_query_param_auth", "nameInDart": "useQueryParamAuth", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 486, "name": "useQueryParamAuth"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 576, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "offset": 656, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["uuid"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Endpoints", "row_class_name": "Endpoint", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/database/tables/namespaces.dart"]}