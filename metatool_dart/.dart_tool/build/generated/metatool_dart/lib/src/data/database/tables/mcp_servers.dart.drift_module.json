{"elements": {"mcp_servers": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 152, "name": "McpServers"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "uuid", "nameInDart": "uuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 196, "name": "uuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 36, "max_length": 36}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 263, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 1, "max_length": 255}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 327, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "type", "nameInDart": "type", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 379, "name": "type"}, "typeConverter": {"expression": {"elements": ["const ", {"lexeme": "EnumIndexConverter", "import_uri": "package:drift/drift.dart"}, "<", {"lexeme": "McpServerType", "import_uri": "package:metatool_dart/src/data/enums/mcp_server_enums.dart"}, ">(", {"lexeme": "McpServerType", "import_uri": "package:metatool_dart/src/data/enums/mcp_server_enums.dart"}, ".values)"]}, "dart_type": 0, "json_type": 1, "sql_type": {"builtin": "int"}, "dart_type_is_nullable": false, "sql_type_is_nullable": false, "json_type_is_nullable": false, "is_drift_enum_converter": true}, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "command", "nameInDart": "command", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 466, "name": "command"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "args", "nameInDart": "args", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 515, "name": "args"}, "typeConverter": {"expression": {"elements": ["const ", {"lexeme": "StringListConverter", "import_uri": "package:metatool_dart/src/data/utils/converters.dart"}, "()"]}, "dart_type": 2, "json_type": null, "sql_type": {"builtin": "string"}, "dart_type_is_nullable": false, "sql_type_is_nullable": false, "json_type_is_nullable": false, "is_drift_enum_converter": false}, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "env", "nameInDart": "env", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 594, "name": "env"}, "typeConverter": {"expression": {"elements": ["const ", {"lexeme": "JsonMapConverter", "import_uri": "package:metatool_dart/src/data/utils/converters.dart"}, "()"]}, "dart_type": 3, "json_type": null, "sql_type": {"builtin": "string"}, "dart_type_is_nullable": false, "sql_type_is_nullable": false, "json_type_is_nullable": false, "is_drift_enum_converter": false}, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "url", "nameInDart": "url", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 672, "name": "url"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "bearer_token", "nameInDart": "bearerToken", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 717, "name": "bearerToken"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "offset": 777, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["uuid"]}, {"type": "unique", "columns": ["name"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "McpServers", "row_class_name": "McpServer", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/enums/mcp_server_enums.dart", "package:metatool_dart/src/data/utils/converters.dart"]}