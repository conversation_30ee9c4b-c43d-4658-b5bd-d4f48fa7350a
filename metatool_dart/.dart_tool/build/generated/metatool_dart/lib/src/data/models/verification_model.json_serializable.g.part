// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VerificationModel _$VerificationModelFromJson(Map<String, dynamic> json) =>
    VerificationModel(
      id: json['id'] as String,
      identifier: json['identifier'] as String,
      value: json['value'] as String,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$VerificationModelToJson(VerificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'identifier': instance.identifier,
      'value': instance.value,
      'expiresAt': instance.expiresAt.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
