{"elements": {"namespaces": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "name": "namespaces"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "offset": 70, "name": "Namespaces"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "uuid", "nameInDart": "uuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "offset": 114, "name": "uuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 36, "max_length": 36}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "offset": 178, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "offset": 222, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "offset": 279, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "offset": 359, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["uuid"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Namespaces", "row_class_name": "Namespace", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart"]}