{"elements": {"namespace_tool_mappings": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "name": "namespace_tool_mappings"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "offset": 199, "name": "NamespaceToolMappings"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "name": "namespaces"}, {"library_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "name": "tools"}, {"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "uuid", "nameInDart": "uuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "offset": 254, "name": "uuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 36, "max_length": 36}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "namespace_uuid", "nameInDart": "namespaceUuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "offset": 318, "name": "namespaceUuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "name": "namespaces"}, "name": "uuid"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "tool_uuid", "nameInDart": "toolUuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "offset": 421, "name": "toolUuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "name": "tools"}, "name": "uuid"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "mcp_server_uuid", "nameInDart": "mcpServerUuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "offset": 514, "name": "mcpServerUuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}, "name": "uuid"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "offset": 616, "name": "status"}, "typeConverter": {"expression": {"elements": ["const ", {"lexeme": "EnumIndexConverter", "import_uri": "package:drift/drift.dart"}, "<", {"lexeme": "McpServerStatus", "import_uri": "package:metatool_dart/src/data/enums/mcp_server_enums.dart"}, ">(", {"lexeme": "McpServerStatus", "import_uri": "package:metatool_dart/src/data/enums/mcp_server_enums.dart"}, ".values)"]}, "dart_type": 0, "json_type": 1, "sql_type": {"builtin": "int"}, "dart_type_is_nullable": false, "sql_type_is_nullable": false, "json_type_is_nullable": false, "is_drift_enum_converter": true}, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "offset": 708, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["uuid"]}, {"type": "unique", "columns": ["namespace_uuid", "tool_uuid"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "NamespaceToolMappings", "row_class_name": "NamespaceToolMapping", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/database/tables/namespaces.dart", "package:metatool_dart/src/data/database/tables/tools.dart", "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "package:metatool_dart/src/data/enums/mcp_server_enums.dart"]}