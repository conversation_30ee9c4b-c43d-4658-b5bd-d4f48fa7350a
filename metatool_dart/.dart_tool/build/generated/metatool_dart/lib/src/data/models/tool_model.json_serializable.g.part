// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ToolModel _$ToolModelFromJson(Map<String, dynamic> json) => ToolModel(
      uuid: json['uuid'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      toolSchema: json['toolSchema'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      mcpServerUuid: json['mcpServerUuid'] as String,
    );

Map<String, dynamic> _$<PERSON>l<PERSON>(ToolModel instance) => <String, dynamic>{
      'uuid': instance.uuid,
      'name': instance.name,
      'description': instance.description,
      'toolSchema': instance.toolSchema,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'mcpServerUuid': instance.mcpServerUuid,
    };
