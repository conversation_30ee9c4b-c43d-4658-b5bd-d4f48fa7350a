{"valid_import": true, "imports": [{"uri": "package:drift/drift.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/tools.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/enums/mcp_server_enums.dart", "transitive": false}], "elements": [{"kind": "table", "name": "namespace_tool_mappings", "dart_name": "NamespaceToolMappings"}]}