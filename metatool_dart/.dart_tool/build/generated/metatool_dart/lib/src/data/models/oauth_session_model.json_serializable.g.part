// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OAuthSessionModel _$OAuthSessionModelFromJson(Map<String, dynamic> json) =>
    OAuthSessionModel(
      uuid: json['uuid'] as String,
      mcpServerUuid: json['mcpServerUuid'] as String,
      clientInformation: json['clientInformation'] as Map<String, dynamic>?,
      tokens: json['tokens'] as Map<String, dynamic>?,
      codeVerifier: json['codeVerifier'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$OAuthSessionModelToJson(OAuthSessionModel instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'mcpServerUuid': instance.mcpServerUuid,
      'clientInformation': instance.clientInformation,
      'tokens': instance.tokens,
      'codeVerifier': instance.codeVerifier,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
