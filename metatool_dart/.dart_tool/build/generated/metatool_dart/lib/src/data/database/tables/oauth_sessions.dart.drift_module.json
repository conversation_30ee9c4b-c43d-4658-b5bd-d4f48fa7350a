{"elements": {"o_auth_sessions": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "name": "o_auth_sessions"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 100, "name": "OAuthSessions"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "uuid", "nameInDart": "uuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 147, "name": "uuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 36, "max_length": 36}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "mcp_server_uuid", "nameInDart": "mcpServerUuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 211, "name": "mcpServerUuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}, "name": "uuid"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "client_information", "nameInDart": "clientInformation", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 314, "name": "clientInformation"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "tokens", "nameInDart": "tokens", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 398, "name": "tokens"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "code_verifier", "nameInDart": "codeVerifier", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 471, "name": "codeVerifier"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 529, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "offset": 609, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["uuid"]}, {"type": "unique", "columns": ["mcp_server_uuid"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "OAuthSessions", "row_class_name": "OAuthSession", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/database/tables/mcp_servers.dart"]}