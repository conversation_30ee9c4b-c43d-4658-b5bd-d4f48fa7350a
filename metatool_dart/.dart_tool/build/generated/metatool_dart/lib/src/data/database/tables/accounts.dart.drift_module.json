{"elements": {"accounts": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "name": "accounts"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 89, "name": "Accounts"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 131, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "account_id", "nameInDart": "accountId", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 164, "name": "accountId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "provider_id", "nameInDart": "providerId", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 204, "name": "providerId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "user_id", "nameInDart": "userId", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 245, "name": "userId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "access_token", "nameInDart": "accessToken", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 334, "name": "accessToken"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "refresh_token", "nameInDart": "refreshToken", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 387, "name": "refreshToken"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "id_token", "nameInDart": "idToken", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 441, "name": "idToken"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "access_token_expires_at", "nameInDart": "accessTokenExpiresAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 494, "name": "accessTokenExpiresAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "refresh_token_expires_at", "nameInDart": "refreshTokenExpiresAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 564, "name": "refreshTokenExpiresAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "scope", "nameInDart": "scope", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 631, "name": "scope"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "password", "nameInDart": "password", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 678, "name": "password"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 732, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "offset": 812, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["id"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Accounts", "row_class_name": "Account", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/database/tables/users.dart"]}