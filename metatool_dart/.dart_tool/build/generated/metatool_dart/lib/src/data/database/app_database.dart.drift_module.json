{"elements": {"AppDatabase": {"id": {"library_uri": "package:metatool_dart/src/data/database/app_database.dart", "name": "AppDatabase"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/app_database.dart", "offset": 878, "name": "AppDatabase"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}, {"library_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "name": "o_auth_sessions"}, {"library_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "name": "tools"}, {"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}, {"library_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "name": "sessions"}, {"library_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "name": "accounts"}, {"library_uri": "package:metatool_dart/src/data/database/tables/verifications.dart", "name": "verifications"}, {"library_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "name": "namespaces"}, {"library_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "name": "endpoints"}, {"library_uri": "package:metatool_dart/src/data/database/tables/namespace_server_mappings.dart", "name": "namespace_server_mappings"}, {"library_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "name": "namespace_tool_mappings"}, {"library_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "name": "api_keys"}, {"library_uri": "package:metatool_dart/src/data/database/tables/config.dart", "name": "config"}], "type": "database", "tables": [{"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}, {"library_uri": "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "name": "o_auth_sessions"}, {"library_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "name": "tools"}, {"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}, {"library_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "name": "sessions"}, {"library_uri": "package:metatool_dart/src/data/database/tables/accounts.dart", "name": "accounts"}, {"library_uri": "package:metatool_dart/src/data/database/tables/verifications.dart", "name": "verifications"}, {"library_uri": "package:metatool_dart/src/data/database/tables/namespaces.dart", "name": "namespaces"}, {"library_uri": "package:metatool_dart/src/data/database/tables/endpoints.dart", "name": "endpoints"}, {"library_uri": "package:metatool_dart/src/data/database/tables/namespace_server_mappings.dart", "name": "namespace_server_mappings"}, {"library_uri": "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "name": "namespace_tool_mappings"}, {"library_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "name": "api_keys"}, {"library_uri": "package:metatool_dart/src/data/database/tables/config.dart", "name": "config"}], "views": [], "includes": [], "queries": [], "schema_version": 1, "daos": [], "has_constructor_arg": false}}, "imports": ["package:drift/drift.dart", "package:drift/native.dart", "package:path_provider/path_provider.dart", "package:path/path.dart", "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "package:metatool_dart/src/data/database/tables/oauth_sessions.dart", "package:metatool_dart/src/data/database/tables/tools.dart", "package:metatool_dart/src/data/database/tables/users.dart", "package:metatool_dart/src/data/database/tables/sessions.dart", "package:metatool_dart/src/data/database/tables/accounts.dart", "package:metatool_dart/src/data/database/tables/verifications.dart", "package:metatool_dart/src/data/database/tables/namespaces.dart", "package:metatool_dart/src/data/database/tables/endpoints.dart", "package:metatool_dart/src/data/database/tables/namespace_server_mappings.dart", "package:metatool_dart/src/data/database/tables/namespace_tool_mappings.dart", "package:metatool_dart/src/data/database/tables/api_keys.dart", "package:metatool_dart/src/data/database/tables/config.dart"]}