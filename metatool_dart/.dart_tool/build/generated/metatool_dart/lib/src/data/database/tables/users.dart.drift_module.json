{"elements": {"users": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 65, "name": "Users"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 104, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 137, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "email", "nameInDart": "email", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 172, "name": "email"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "email_verified", "nameInDart": "emailVerified", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 217, "name": "emailVerified"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "image", "nameInDart": "image", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 299, "name": "image"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 350, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/users.dart", "offset": 430, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["id"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Users", "row_class_name": "User", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart"]}