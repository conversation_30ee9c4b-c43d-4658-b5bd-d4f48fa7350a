{"elements": {"sessions": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "name": "sessions"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 89, "name": "Sessions"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 131, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "expires_at", "nameInDart": "expiresAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 168, "name": "expiresAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "token", "nameInDart": "token", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 212, "name": "token"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 261, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 341, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "ip_address", "nameInDart": "ip<PERSON><PERSON><PERSON>", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 417, "name": "ip<PERSON><PERSON><PERSON>"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "user_agent", "nameInDart": "userAgent", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 468, "name": "userAgent"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "user_id", "nameInDart": "userId", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/sessions.dart", "offset": 519, "name": "userId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["id"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Sessions", "row_class_name": "Session", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/database/tables/users.dart"]}