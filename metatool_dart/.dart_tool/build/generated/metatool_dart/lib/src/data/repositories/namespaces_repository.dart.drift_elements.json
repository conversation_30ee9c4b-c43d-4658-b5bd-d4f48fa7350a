{"valid_import": true, "imports": [{"uri": "package:drift/drift.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/utils/uuid.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/database/app_database.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/models/namespace_model.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/exceptions/database_exception.dart", "transitive": false}], "elements": []}