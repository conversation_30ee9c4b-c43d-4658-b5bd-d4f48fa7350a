{"elements": {"api_keys": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "name": "api_keys"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "offset": 88, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "uuid", "nameInDart": "uuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "offset": 129, "name": "uuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 36, "max_length": 36}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "offset": 193, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "key", "nameInDart": "key", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "offset": 228, "name": "key"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "user_id", "nameInDart": "userId", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "offset": 271, "name": "userId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/users.dart", "name": "users"}, "name": "id"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "offset": 364, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_active", "nameInDart": "isActive", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/api_keys.dart", "offset": 440, "name": "isActive"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(true)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["uuid"]}, {"type": "unique", "columns": ["user_id", "name"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "row_class_name": "<PERSON><PERSON><PERSON><PERSON>", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/database/tables/users.dart"]}