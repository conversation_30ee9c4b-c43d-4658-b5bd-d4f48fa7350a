{"elements": {"tools": {"id": {"library_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "name": "tools"}, "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 92, "name": "Tools"}, "references": [{"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "uuid", "nameInDart": "uuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 131, "name": "uuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 36, "max_length": 36}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 195, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 230, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "tool_schema", "nameInDart": "toolSchema", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 283, "name": "toolSchema"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 353, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 433, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "mcp_server_uuid", "nameInDart": "mcpServerUuid", "declaration": {"source_uri": "package:metatool_dart/src/data/database/tables/tools.dart", "offset": 509, "name": "mcpServerUuid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:metatool_dart/src/data/database/tables/mcp_servers.dart", "name": "mcp_servers"}, "name": "uuid"}, "onUpdate": null, "onDelete": "cascade", "initiallyDeferred": false}], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["uuid"]}, {"type": "unique", "columns": ["mcp_server_uuid", "name"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Tools", "row_class_name": "Tool", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:metatool_dart/src/data/database/tables/mcp_servers.dart"]}