{"valid_import": true, "imports": [{"uri": "package:metatool_dart/src/mcp_core/metamcp_server.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/mcp_server_service.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/mcp_client_factory.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/metamcp_client.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/session_manager.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/mcp_proxy.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/config_service.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/fetch_metamcp.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/middleware/middleware.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/middleware/filter_tools_middleware.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/utils/mcp_utils.dart", "transitive": true}, {"uri": "package:metatool_dart/src/mcp_core/utils/environment_utils.dart", "transitive": true}], "elements": []}