{"valid_import": true, "imports": [{"uri": "package:mcp_dart/mcp_dart.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/enums/mcp_server_enums.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/repositories/mcp_servers_repository.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/repositories/namespace_mappings_repository.dart", "transitive": false}, {"uri": "package:metatool_dart/src/mcp_core/middleware/middleware.dart", "transitive": false}], "elements": []}