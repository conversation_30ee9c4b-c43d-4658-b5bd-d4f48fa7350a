{"elements": {}, "imports": ["package:metatool_dart/src/mcp_core/metamcp_server.dart", "package:metatool_dart/src/mcp_core/mcp_server_service.dart", "package:metatool_dart/src/mcp_core/mcp_client_factory.dart", "package:metatool_dart/src/mcp_core/metamcp_client.dart", "package:metatool_dart/src/mcp_core/session_manager.dart", "package:metatool_dart/src/mcp_core/mcp_proxy.dart", "package:metatool_dart/src/mcp_core/config_service.dart", "package:metatool_dart/src/mcp_core/fetch_metamcp.dart", "package:metatool_dart/src/mcp_core/middleware/middleware.dart", "package:metatool_dart/src/mcp_core/middleware/filter_tools_middleware.dart", "package:metatool_dart/src/mcp_core/utils/mcp_utils.dart", "package:metatool_dart/src/mcp_core/utils/environment_utils.dart"]}