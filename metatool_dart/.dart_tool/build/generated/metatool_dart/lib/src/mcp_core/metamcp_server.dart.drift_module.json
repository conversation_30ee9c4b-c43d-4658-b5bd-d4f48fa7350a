{"elements": {}, "imports": ["package:mcp_dart/mcp_dart.dart", "package:metatool_dart/src/data/models/tool_model.dart", "package:metatool_dart/src/data/repositories/mcp_servers_repository.dart", "package:metatool_dart/src/data/repositories/namespace_mappings_repository.dart", "package:metatool_dart/src/data/repositories/tools_repository.dart", "package:metatool_dart/src/mcp_core/mcp_client_factory.dart", "package:metatool_dart/src/mcp_core/mcp_server_service.dart", "package:metatool_dart/src/mcp_core/middleware/filter_tools_middleware.dart", "package:metatool_dart/src/mcp_core/middleware/middleware.dart", "package:metatool_dart/src/mcp_core/session_manager.dart", "package:metatool_dart/src/mcp_core/utils/environment_utils.dart"]}