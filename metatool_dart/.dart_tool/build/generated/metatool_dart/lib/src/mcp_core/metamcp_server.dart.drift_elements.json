{"valid_import": true, "imports": [{"uri": "package:mcp_dart/mcp_dart.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/models/tool_model.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/repositories/mcp_servers_repository.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/repositories/namespace_mappings_repository.dart", "transitive": false}, {"uri": "package:metatool_dart/src/data/repositories/tools_repository.dart", "transitive": false}, {"uri": "package:metatool_dart/src/mcp_core/mcp_client_factory.dart", "transitive": false}, {"uri": "package:metatool_dart/src/mcp_core/mcp_server_service.dart", "transitive": false}, {"uri": "package:metatool_dart/src/mcp_core/middleware/filter_tools_middleware.dart", "transitive": false}, {"uri": "package:metatool_dart/src/mcp_core/middleware/middleware.dart", "transitive": false}, {"uri": "package:metatool_dart/src/mcp_core/session_manager.dart", "transitive": false}, {"uri": "package:metatool_dart/src/mcp_core/utils/environment_utils.dart", "transitive": false}], "elements": []}