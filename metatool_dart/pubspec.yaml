name: metatool_dart
description: MetaMCP Database Layer in Dart using SQLite
version: 1.0.0

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  # Database ORM - drift is the most mature Dart SQL library
  drift: ^2.16.0
  sqlite3: ^2.4.0
  path_provider: ^2.1.2
  path: ^1.9.0
  
  # Serialization and JSON
  json_annotation: ^4.8.1
  
  # HTTP Framework
  shelf: ^1.4.1
  shelf_router: ^1.1.4
  
  # Utilities
  uuid: ^4.1.0
  crypto: ^3.0.3
  meta: ^1.11.0
  nanoid: ^1.0.0
  
  # Validation
  validators: ^3.0.0
  mcp_dart: ^0.5.2

dev_dependencies:
  # Code generation
  drift_dev: ^2.16.0
  build_runner: ^2.4.9
  json_serializable: ^6.7.1
  
  # Testing and linting
  test: ^1.24.0
  lints: ^5.0.0
  mocktail: ^1.0.3 