import 'dart:async';

import 'package:mcp_dart/mcp_dart.dart';

import '../data/enums/mcp_server_enums.dart';
import 'mcp_server_service.dart';
import 'utils/environment_utils.dart';

/// A data class to hold a connected client and its cleanup resources.
class ConnectedClient {
  final Client client;
  final Future<void> Function() cleanup;

  ConnectedClient({required this.client, required this.cleanup});
}

/// A factory responsible for creating and connecting MCP clients
/// based on server parameters.
class McpClientFactory {


  /// Creates and connects an MCP client with a retry mechanism.
  Future<ConnectedClient?> createAndConnect(ServerParameters params) async {
    // 1. Create Transport based on server type
    Transport? transport;
    switch (params.type) {
      case McpServerType.stdio:
        if (params.command == null || params.command!.isEmpty) {
          print('STDIO server is missing command, skipping');
          return null;
        }
        
        // Environment is handled by the transport itself
        
        transport = StdioClientTransport(
          StdioServerParameters(
            command: params.command!,
            args: params.args ?? [],
          ),
        );
        break;
      case McpServerType.sse:
      case McpServerType.streamableHttp:
        if (params.url == null || params.url!.isEmpty) {
          print(
              '${params.type.name} server ${params.uuid} is missing url field, skipping');
          return null;
        }
        final transformedUrl = EnvironmentUtils.transformDockerUrl(params.url!);
        
        // Create transport options with headers if needed
        StreamableHttpClientTransportOptions? options;
        if (params.oauthTokens != null) {
          options = StreamableHttpClientTransportOptions(
            requestInit: {
              'headers': {
                'Authorization': 'Bearer ${params.oauthTokens!.accessToken}',
              },
            },
          );
        }
        
        transport = StreamableHttpClientTransport(
          Uri.parse(transformedUrl),
          opts: options,
        );
        break;
    }

    if (transport == null) {
      print('Could not create transport for server ${params.name}');
      return null;
    }

    // 2. Create the Client
    final client = Client(
      Implementation(name: "metamcp-client", version: "2.0.0"),
      options: ClientOptions(
        // The Dart SDK v0.5.2 ClientCapabilities are different from the TS SDK's.
        // We will rely on the server to announce its capabilities.
        capabilities: ClientCapabilities(),
      ),
    );

    // 3. Connect with retry logic
    const retries = 3;
    const wait = Duration(milliseconds: 2500);

    for (var i = 0; i < retries; i++) {
      try {
        await client.connect(transport);
        return ConnectedClient(
          client: client,
          cleanup: () async {
            await transport!.close();
            await client.close();
          },
        );
      } catch (e) {
        print('Error connecting to MetaMCP client (${params.name}): $e');
        if (i < retries - 1) {
          try {
            await client.close();
          } catch (_) {
            /* ignore */
          }
          await Future.delayed(wait);
        }
      }
    }

    print('Failed to connect to ${params.name} after all retries.');
    return null; // Failed to connect after all retries
  }
} 