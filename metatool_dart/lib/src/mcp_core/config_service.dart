import '../data/repositories/config_repository.dart';
import '../data/models/config_model.dart';

/// Configuration service for managing application configuration settings.
/// This service provides a high-level API for accessing and modifying
/// configuration values stored in the database.
class ConfigService {
  final ConfigRepository _configRepository;

  ConfigService({required ConfigRepository configRepository})
      : _configRepository = configRepository;

  /// Checks if user signup is disabled.
  Future<bool> isSignupDisabled() async {
    final config = await _configRepository.getConfig('DISABLE_SIGNUP');
    return config?.value == 'true';
  }

  /// Sets whether user signup is disabled.
  Future<void> setSignupDisabled(bool disabled) async {
    await _configRepository.setConfig(
      id: 'DISABLE_SIGNUP',
      value: disabled.toString(),
      description: 'Whether new user signup is disabled',
    );
  }

  /// Gets a configuration value by key.
  Future<String?> getConfig(String key) async {
    final config = await _configRepository.getConfig(key);
    return config?.value;
  }

  /// Sets a configuration value.
  Future<void> setConfig(
    String key,
    String value, [
    String? description,
  ]) async {
    await _configRepository.setConfig(
      id: key,
      value: value,
      description: description,
    );
  }

  /// Gets all configuration entries.
  Future<List<ConfigModel>> getAllConfigs() async {
    return await _configRepository.getAllConfigs();
  }
} 