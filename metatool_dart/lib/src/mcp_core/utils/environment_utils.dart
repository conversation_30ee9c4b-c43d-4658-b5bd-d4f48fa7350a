import 'dart:io';

/// Environment variables to inherit by default when an environment 
/// is not explicitly given for an MCP server.
class EnvironmentUtils {
  /// Default inherited environment variables for Windows
  static const List<String> _windowsDefaultVars = [
    'APPDATA',
    'HOMEDRIVE', 
    'HOMEPATH',
    'LOCALAPPDATA',
    'PATH',
    'PROCESSOR_ARCHITECTURE',
    'SYSTEMDRIVE',
    'SYSTEMROOT',
    'TEMP',
    'USERNAME',
    'USERPROFILE',
  ];

  /// Default inherited environment variables for Unix-like systems
  /// (list inspired by the default env inheritance of sudo)
  static const List<String> _unixDefaultVars = [
    'HOME',
    'LOGNAME', 
    'PATH',
    'SHELL',
    'TERM',
    'USER',
  ];

  /// Gets the default environment variables based on the current platform
  static List<String> get defaultInheritedEnvVars {
    return Platform.isWindows ? _windowsDefaultVars : _unixDefaultVars;
  }

  /// Returns a default environment object including only environment 
  /// variables deemed safe to inherit.
  static Map<String, String> getDefaultEnvironment() {
    final env = <String, String>{};

    for (final key in defaultInheritedEnvVars) {
      final value = Platform.environment[key];
      if (value == null) {
        continue;
      }

      // Skip functions, which are a security risk
      if (value.startsWith('()')) {
        continue;
      }

      env[key] = value;
    }

    return env;
  }

  /// Sanitizes a name by replacing non-alphanumeric characters with underscores
  static String sanitizeName(String name) {
    return name.replaceAll(RegExp(r'[^a-zA-Z0-9_-]'), '_');
  }

  /// Transforms localhost URLs to use host.docker.internal when running inside Docker
  static String transformDockerUrl(String url) {
    if (Platform.environment['TRANSFORM_LOCALHOST_TO_DOCKER_INTERNAL'] == 'true') {
      final transformed = url.replaceAll(
        RegExp(r'localhost|127\.0\.0\.1'),
        'host.docker.internal',
      );
      print('Docker URL transformation: $url -> $transformed');
      return transformed;
    }
    print('Docker URL transformation disabled: $url');
    return url;
  }
} 