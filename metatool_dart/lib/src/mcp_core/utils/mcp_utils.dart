import 'dart:convert';
import 'package:crypto/crypto.dart';

import '../mcp_server_service.dart';
import '../../data/enums/mcp_server_enums.dart';

String _computeParamsHash(ServerParameters params) {
  Map<String, dynamic> paramsDict;

  switch (params.type) {
    case McpServerType.stdio:
      // Sort env map for consistent hashing
      final sortedEnv = params.env != null
          ? Map.fromEntries(
              params.env!.entries.toList()..sort((a, b) => a.key.compareTo(b.key)))
          : null;
      paramsDict = {
        'uuid': params.uuid,
        'type': 'STDIO', // Match TS implementation for consistent hashing
        'command': params.command,
        'args': params.args,
        'env': sortedEnv,
      };
      break;
    case McpServerType.sse:
    case McpServerType.streamableHttp:
      paramsDict = {
        'uuid': params.uuid,
        'type': params.type.name,
        'url': params.url,
      };
      break;
  }

  final paramsJson = json.encode(paramsDict);
  final bytes = utf8.encode(paramsJson);
  final digest = sha256.convert(bytes);
  return digest.toString();
}

String getSessionKey(ServerParameters params) {
  return '${params.uuid}_${_computeParamsHash(params)}';
} 