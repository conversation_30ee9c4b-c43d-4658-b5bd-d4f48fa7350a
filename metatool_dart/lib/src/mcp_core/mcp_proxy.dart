import 'dart:async';
import 'package:mcp_dart/mcp_dart.dart';

/// Error handler for all types of errors
void _onError(dynamic error) {
  final errorMessage = error.toString();
  if (errorMessage.contains('HTTP 404') || errorMessage.contains('ECONNREFUSED')) {
    print('Connection refused. Is the MCP server running?');
  } else {
    print('Error: $error');
  }
}

/// Creates a proxy between client and server transports.
/// This handles bidirectional message passing with error handling.
class McpProxy {
  final Transport _transportToClient;
  final Transport _transportToServer;
  
  bool _transportToClientClosed = false;
  bool _transportToServerClosed = false;
  bool _reportedServerSession = false;

  McpProxy({
    required Transport transportToClient,
    required Transport transportToServer,
  })  : _transportToClient = transportToClient,
        _transportToServer = transportToServer;

  /// Initializes the proxy by setting up message handlers and error handlers.
  void initialize() {
    // Handle messages from client to server
    _transportToClient.onmessage = (JsonRpcMessage message) {
      _transportToServer.send(message).catchError((error) {
        // Send error response back to client if it was a request (has id) and connection is still open
        if (message is JsonRpcRequest && !_transportToClientClosed) {
          final errorResponse = JsonRpcError(
            id: message.id,
            error: JsonRpcErrorData(
              code: -32001,
              message: error.toString(),
              data: error.toString(),
            ),
          );
          _transportToClient.send(errorResponse).catchError(_onError);
        }
      });
    };

    // Handle messages from server to client
    _transportToServer.onmessage = (JsonRpcMessage message) {
      if (!_reportedServerSession) {
        // Can only report for StreamableHttp
        if (_transportToServer.sessionId != null) {
          print('Proxy  <-> Server sessionId: ${_transportToServer.sessionId}');
        }
        _reportedServerSession = true;
      }
      _transportToClient.send(message).catchError(_onError);
    };

    // Handle client connection close
    _transportToClient.onclose = () {
      if (_transportToServerClosed) return;
      _transportToClientClosed = true;
      _transportToServer.close().catchError(_onError);
    };

    // Handle server connection close
    _transportToServer.onclose = () {
      if (_transportToClientClosed) return;
      _transportToServerClosed = true;
      _transportToClient.close().catchError(_onError);
    };

    // Set error handlers
    _transportToClient.onerror = _onError;
    _transportToServer.onerror = _onError;
  }

  /// Starts the proxy operation.
  Future<void> start() async {
    initialize();
  }

  /// Stops the proxy and closes both transports.
  Future<void> stop() async {
    await Future.wait([
      _transportToClient.close().catchError(_onError),
      _transportToServer.close().catchError(_onError),
    ]);
  }
} 