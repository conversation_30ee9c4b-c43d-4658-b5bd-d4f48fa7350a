import 'dart:async';

import 'package:mcp_dart/mcp_dart.dart';

import '../../data/enums/mcp_server_enums.dart';
import '../../data/repositories/mcp_servers_repository.dart';
import '../../data/repositories/namespace_mappings_repository.dart';
import 'middleware.dart';

// Helper record for parsed tool names
typedef _ParsedToolName = ({String serverName, String originalToolName});

// --- Cache Implementation ---
class _ToolStatusCache {
  final _cache = <String, McpServerStatus>{};
  final _expiry = <String, DateTime>{};
  final Duration ttl = const Duration(seconds: 1);

  _ToolStatusCache();

  String _getCacheKey(String nsUuid, String toolName, String serverUuid) =>
      '$nsUuid:$serverUuid:$toolName';

  McpServerStatus? get(String nsUuid, String toolName, String serverUuid) {
    final key = _getCacheKey(nsUuid, toolName, serverUuid);
    final expiryTime = _expiry[key];
    if (expiryTime == null || DateTime.now().isAfter(expiryTime)) {
      _cache.remove(key);
      _expiry.remove(key);
      return null;
    }
    return _cache[key];
  }

  void set(
      String nsUuid, String toolName, String serverUuid, McpServerStatus status) {
    final key = _getCacheKey(nsUuid, toolName, serverUuid);
    _cache[key] = status;
    _expiry[key] = DateTime.now().add(ttl);
  }
}

// --- Helper Functions ---

_ParsedToolName? _parseToolName(String fullName) {
  final parts = fullName.split('__');
  if (parts.length < 2) return null;
  return (
    serverName: parts.first,
    originalToolName: parts.sublist(1).join('__')
  );
}

// --- Middleware Creation ---

/// Creates a middleware that filters the list of tools based on their status
/// in the current namespace.
ListToolsMiddleware createFilterListToolsMiddleware({
  required NamespaceMappingsRepository namespaceMappingsRepo,
  required McpServersRepository mcpServersRepo,
  bool useCache = true,
}) {
  final cache = _ToolStatusCache();

  Future<McpServerStatus?> getStatus(
      String nsUuid, String toolName, String serverUuid) async {
    if (useCache) {
      final cachedStatus = cache.get(nsUuid, toolName, serverUuid);
      if (cachedStatus != null) return cachedStatus;
    }
    final dbStatus =
        await namespaceMappingsRepo.getToolStatus(nsUuid, toolName, serverUuid);
    if (dbStatus != null && useCache) {
      cache.set(nsUuid, toolName, serverUuid, dbStatus);
    }
    return dbStatus;
  }

  return (handler) {
    return (request, context) async {
      final response = await handler(request, context);
      if (response.tools.isEmpty) return response;

      final toolChecks = response.tools.map((tool) async {
        final parsed = _parseToolName(tool.name);
        if (parsed == null) return tool; // Keep if format is wrong

        // This could be optimized by fetching all servers once.
        // For now, mirroring TS logic.
        final server = await mcpServersRepo.findByName(parsed.serverName);
        if (server == null) return tool; // Keep if server not found

        final status = await getStatus(
            context.namespaceUuid, parsed.originalToolName, server.uuid);

        if (status == null || status == McpServerStatus.active) {
          return tool;
        }
        return null; // Filter out inactive tool
      }).toList();

      final results = await Future.wait(toolChecks);
      final activeTools = results.whereType<Tool>().toList();

      return ListToolsResult(
        tools: activeTools,
        nextCursor: response.nextCursor,
      );
    };
  };
}

/// Creates a middleware that blocks calls to tools that are marked as inactive
/// in the current namespace.
CallToolMiddleware createFilterCallToolMiddleware({
  required NamespaceMappingsRepository namespaceMappingsRepo,
  required McpServersRepository mcpServersRepo,
  bool useCache = true,
  String Function(String toolName, String reason)? customErrorMessage,
}) {
  final cache = _ToolStatusCache();

  Future<McpServerStatus?> getStatus(
      String nsUuid, String toolName, String serverUuid) async {
    if (useCache) {
      final cachedStatus = cache.get(nsUuid, toolName, serverUuid);
      if (cachedStatus != null) return cachedStatus;
    }
    final dbStatus =
        await namespaceMappingsRepo.getToolStatus(nsUuid, toolName, serverUuid);
    if (dbStatus != null && useCache) {
      cache.set(nsUuid, toolName, serverUuid, dbStatus);
    }
    return dbStatus;
  }

  return (handler) {
    return (request, context) async {
      final toolName = request.callParams.name;
      final parsed = _parseToolName(toolName);

      if (parsed != null) {
        final server = await mcpServersRepo.findByName(parsed.serverName);
        if (server != null) {
          final status = await getStatus(
              context.namespaceUuid, parsed.originalToolName, server.uuid);

          if (status == McpServerStatus.inactive) {
            final reason = 'Tool has been marked as inactive in this namespace';
            final errorMessage = customErrorMessage?.call(toolName, reason) ??
                'Tool "$toolName" is currently inactive: $reason';
            return CallToolResult(
              content: [TextContent(text: errorMessage)],
            );
          }
        }
      }
      // If parsing fails, or server not found, or status is active/null, proceed.
      return handler(request, context);
    };
  };
} 