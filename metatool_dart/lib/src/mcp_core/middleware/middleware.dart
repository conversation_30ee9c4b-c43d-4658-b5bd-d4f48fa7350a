import 'package:mcp_dart/mcp_dart.dart';

// Base context for all handlers
class MetaMcpHandlerContext {
  final String namespaceUuid;
  final String sessionId;

  MetaMcpHandlerContext({
    required this.namespaceUuid,
    required this.sessionId,
  });
}

// Handler function types
typedef ListToolsHandler = Future<ListToolsResult> Function(
    JsonRpcRequest request, MetaMcpHandlerContext context);

typedef CallToolHandler = Future<CallToolResult> Function(
    JsonRpcCallToolRequest request, MetaMcpHandlerContext context);

// Middleware function types that can transform request/response
typedef ListToolsMiddleware = ListToolsHandler Function(ListToolsHandler handler);
typedef CallToolMiddleware = CallToolHandler Function(CallToolHandler handler);

/// Compose multiple middleware functions together.
/// The functions are applied from right to left.
T compose<T extends Function>(List<T Function(T)> middlewares, T handler) {
  if (middlewares.isEmpty) {
    return handler;
  }
  return middlewares.reversed.fold(handler, (acc, middleware) => middleware(acc));
} 