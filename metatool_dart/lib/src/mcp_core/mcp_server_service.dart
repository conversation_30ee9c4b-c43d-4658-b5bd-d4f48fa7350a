import '../data/enums/mcp_server_enums.dart';
import '../data/models/mcp_server_model.dart';
import '../data/models/oauth_session_model.dart';
import '../data/repositories/mcp_servers_repository.dart';
import '../data/repositories/oauth_sessions_repository.dart';

/// A data class to hold OAuth token information.
class OAuthTokenData {
  final String accessToken;
  final String tokenType;
  final int? expiresIn;
  final String? scope;
  final String? refreshToken;

  OAuthTokenData({
    required this.accessToken,
    required this.tokenType,
    this.expiresIn,
    this.scope,
    this.refreshToken,
  });

  factory OAuthTokenData.fromModel(Map<String, dynamic> tokenMap) {
    return OAuthTokenData(
      accessToken: tokenMap['access_token'] as String,
      tokenType: tokenMap['token_type'] as String,
      expiresIn: tokenMap['expires_in'] as int?,
      scope: tokenMap['scope'] as String?,
      refreshToken: tokenMap['refresh_token'] as String?,
    );
  }
}

/// A data class holding all parameters needed to connect to an MCP server.
/// This is the Dart equivalent of the `ServerParameters` interface in TypeScript.
class ServerParameters {
  final String uuid;
  final String name;
  final String? description;
  final McpServerType type;
  final String? command;
  final List<String>? args;
  final Map<String, String>? env;
  final String? url;
  final DateTime createdAt;
  final McpServerStatus status;
  final OAuthTokenData? oauthTokens;

  ServerParameters({
    required this.uuid,
    required this.name,
    this.description,
    required this.type,
    this.command,
    this.args,
    this.env,
    this.url,
    required this.createdAt,
    required this.status,
    this.oauthTokens,
  });

  factory ServerParameters.fromModels({
    required McpServerModel serverModel,
    required McpServerStatus serverStatus,
    OAuthSessionModel? oauthSession,
  }) {
    final tokenMap = oauthSession?.tokens;
    return ServerParameters(
      uuid: serverModel.uuid,
      name: serverModel.name,
      description: serverModel.description,
      type: serverModel.type,
      command: serverModel.command,
      args: serverModel.args,
      env: serverModel.env,
      url: serverModel.url,
      createdAt: serverModel.createdAt,
      status: serverStatus,
      oauthTokens:
          tokenMap != null ? OAuthTokenData.fromModel(tokenMap) : null,
    );
  }
}

/// A service responsible for fetching and constructing `ServerParameters`.
/// It encapsulates the logic of joining data from multiple repositories.
class McpServerService {
  final McpServersRepository _mcpServersRepository;
  final OAuthSessionsRepository _oauthSessionsRepository;

  McpServerService({
    required McpServersRepository mcpServersRepository,
    required OAuthSessionsRepository oauthSessionsRepository,
  })  : _mcpServersRepository = mcpServersRepository,
        _oauthSessionsRepository = oauthSessionsRepository;

  /// Fetches all MCP servers for a given namespace and constructs their
  /// connection parameters.
  Future<Map<String, ServerParameters>> getMcpServers(
    String namespaceUuid, {
    bool includeInactive = false,
  }) async {
    // 1. Fetch server data and status using the JOIN query
    final serverResults = await _mcpServersRepository.getServersByNamespace(
      namespaceUuid,
      includeInactive: includeInactive,
    );

    final serverParamsList = <ServerParameters>[];

    // 2. For each server, fetch its OAuth tokens
    await Future.wait(serverResults.map((result) async {
      final oauthSession =
          await _oauthSessionsRepository.findByMcpServerUuid(result.server.uuid);

      final serverParams = ServerParameters.fromModels(
        serverModel: result.server,
        serverStatus: result.status,
        oauthSession: oauthSession,
      );
      serverParamsList.add(serverParams);
    }));

    // 3. Convert the list to a Map, keyed by server UUID
    return {for (var params in serverParamsList) params.uuid: params};
  }
} 