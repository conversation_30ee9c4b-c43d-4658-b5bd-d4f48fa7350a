import 'mcp_client_factory.dart';
import 'mcp_server_service.dart';
import 'utils/mcp_utils.dart';

/// Manages and caches active MCP client connections for different sessions.
/// This prevents creating new connections for every request.
class SessionManager {
  final McpClientFactory _clientFactory;
  final McpServerService _serverService;

  // Two-level cache: sessionId -> (sessionKey -> ConnectedClient)
  final _sessionConnections = <String, Map<String, ConnectedClient>>{};

  SessionManager({
    required McpClientFactory clientFactory,
    required McpServerService serverService,
  })  : _clientFactory = clientFactory,
        _serverService = serverService;

  /// Gets a cached client connection for a given server in a specific session.
  /// If a connection doesn't exist, it creates, caches, and returns a new one.
  Future<ConnectedClient?> getSession(
      String sessionId, ServerParameters params) async {
    // Ensure session entry exists
    _sessionConnections.putIfAbsent(sessionId, () => {});
    final sessionCache = _sessionConnections[sessionId]!;

    final sessionKey = getSessionKey(params);

    // Return existing connection if found
    if (sessionCache.containsKey(sessionKey)) {
      return sessionCache[sessionKey];
    }

    // Clean up old connections for the same server UUID if params have changed
    final oldSessionKeys = sessionCache.keys
        .where((key) => key.startsWith('${params.uuid}_'))
        .toList();

    for (final oldKey in oldSessionKeys) {
      await sessionCache[oldKey]?.cleanup();
      sessionCache.remove(oldKey);
    }

    // Create new connection
    final newClient = await _clientFactory.createAndConnect(params);
    if (newClient != null) {
      sessionCache[sessionKey] = newClient;
    }

    return newClient;
  }

  /// Pre-warms the session cache by establishing connections to all active
  /// servers in a given namespace.
  Future<void> initSessionConnections(
      String sessionId, String namespaceUuid) async {
    final serverParamsMap = await _serverService.getMcpServers(namespaceUuid);

    await Future.wait(
      serverParamsMap.values.map((params) async {
        try {
          await getSession(sessionId, params);
        } catch (_) {
          // Ignore errors during initialization
        }
      }),
    );
  }

  /// Closes all connections for a specific session and removes it from the cache.
  Future<void> cleanupSessionConnections(String sessionId) async {
    final sessionCache = _sessionConnections[sessionId];
    if (sessionCache == null) return;

    await Future.wait(
        sessionCache.values.map((client) async => await client.cleanup()));

    _sessionConnections.remove(sessionId);
  }

  /// Cleans up all connections across all sessions.
  Future<void> cleanupAllSessions() async {
    await Future.wait(_sessionConnections.keys
        .map((sessionId) async => await cleanupSessionConnections(sessionId)));
  }

  // --- Methods for debugging/monitoring ---

  /// Gets all active session IDs.
  List<String> getActiveSessionIds() {
    return _sessionConnections.keys.toList();
  }

  /// Gets all server connections for a specific session.
  Map<String, ConnectedClient>? getSessionConnections(String sessionId) {
    return _sessionConnections[sessionId];
  }
} 