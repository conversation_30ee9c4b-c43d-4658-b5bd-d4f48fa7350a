import 'dart:convert';
import 'dart:io';

import '../data/enums/mcp_server_enums.dart';
import 'mcp_server_service.dart';

/// Error types for MetaMCP operations
enum FetchError {
  networkError,
  parseError,
  validationError,
  notFound,
}

/// Exception class for MetaMCP fetch operations
class MetaMcpFetchException implements Exception {
  final FetchError type;
  final String message;
  final dynamic originalError;

  MetaMcpFetchException({
    required this.type,
    required this.message,
    this.originalError,
  });

  @override
  String toString() => 'MetaMcpFetchException: $message';
}

/// Fetches MetaMCP server configuration from a URL or file path
class MetaMcpFetcher {
  /// Fetches server parameters from a URL
  static Future<List<ServerParameters>> fetchFromUrl(String url) async {
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse(url));
      final response = await request.close();

      if (response.statusCode != 200) {
        throw MetaMcpFetchException(
          type: FetchError.networkError,
          message: 'HTTP ${response.statusCode}: Failed to fetch from $url',
        );
      }

      final content = await response.transform(utf8.decoder).join();
      client.close();

      return _parseServerConfig(content);
    } catch (e) {
      if (e is MetaMcpFetchException) rethrow;

      throw MetaMcpFetchException(
        type: FetchError.networkError,
        message: 'Failed to fetch from $url: $e',
        originalError: e,
      );
    }
  }

  /// Fetches server parameters from a local file
  static Future<List<ServerParameters>> fetchFromFile(String filePath) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        throw MetaMcpFetchException(
          type: FetchError.notFound,
          message: 'Configuration file not found: $filePath',
        );
      }

      final content = await file.readAsString();
      return _parseServerConfig(content);
    } catch (e) {
      if (e is MetaMcpFetchException) rethrow;

      throw MetaMcpFetchException(
        type: FetchError.networkError,
        message: 'Failed to read file $filePath: $e',
        originalError: e,
      );
    }
  }

  /// Parses server configuration from JSON content
  static List<ServerParameters> _parseServerConfig(String content) {
    try {
      final json = jsonDecode(content);
      
      if (json is! Map<String, dynamic>) {
        throw MetaMcpFetchException(
          type: FetchError.parseError,
          message: 'Invalid JSON structure: expected object',
        );
      }

      final servers = json['servers'];
      if (servers is! List) {
        throw MetaMcpFetchException(
          type: FetchError.parseError,
          message: 'Invalid JSON structure: servers must be an array',
        );
      }

      return servers
          .map((server) => _parseServerParameters(server))
          .where((params) => params != null)
          .cast<ServerParameters>()
          .toList();
    } catch (e) {
      if (e is MetaMcpFetchException) rethrow;

      throw MetaMcpFetchException(
        type: FetchError.parseError,
        message: 'Failed to parse JSON: $e',
        originalError: e,
      );
    }
  }

  /// Parses individual server parameters from JSON
  static ServerParameters? _parseServerParameters(dynamic serverJson) {
    try {
      if (serverJson is! Map<String, dynamic>) {
        print('Warning: Invalid server configuration, skipping');
        return null;
      }

      final uuid = serverJson['uuid'] as String?;
      final name = serverJson['name'] as String?;
      final description = serverJson['description'] as String?;

      if (uuid == null || name == null) {
        print('Warning: Server missing required fields (uuid, name), skipping');
        return null;
      }

      // Parse server type
      McpServerType? type;
      final typeStr = serverJson['type'] as String?;
      if (typeStr != null) {
        switch (typeStr.toUpperCase()) {
          case 'STDIO':
            type = McpServerType.stdio;
            break;
          case 'SSE':
            type = McpServerType.sse;
            break;
          case 'STREAMABLE_HTTP':
            type = McpServerType.streamableHttp;
            break;
          default:
            print('Warning: Unknown server type $typeStr, defaulting to STDIO');
            type = McpServerType.stdio;
        }
      }

      // Parse OAuth tokens if present
      OAuthTokenData? oauthTokens;
      final oauthJson = serverJson['oauth_tokens'] as Map<String, dynamic>?;
      if (oauthJson != null) {
        oauthTokens = OAuthTokenData(
          accessToken: oauthJson['access_token'] as String? ?? '',
          tokenType: oauthJson['token_type'] as String? ?? 'Bearer',
          refreshToken: oauthJson['refresh_token'] as String?,
          expiresIn: oauthJson['expires_in'] as int?,
          scope: oauthJson['scope'] as String?,
        );
      }

      return ServerParameters(
        uuid: uuid,
        name: name,
        description: description,
        type: type ?? McpServerType.stdio,
        command: serverJson['command'] as String?,
        args: (serverJson['args'] as List?)?.cast<String>(),
        url: serverJson['url'] as String?,
        env: (serverJson['env'] as Map<String, dynamic>?)?.cast<String, String>(),
        createdAt: DateTime.now(),
        status: McpServerStatus.active,
        oauthTokens: oauthTokens,
      );
    } catch (e) {
      print('Warning: Failed to parse server configuration: $e');
      return null;
    }
  }

  /// Validates server parameters
  static bool validateServerParameters(ServerParameters params) {
    if (params.uuid.isEmpty || params.name.isEmpty) {
      return false;
    }

    switch (params.type) {
      case McpServerType.stdio:
        return params.command != null && params.command!.isNotEmpty;
      case McpServerType.sse:
      case McpServerType.streamableHttp:
        return params.url != null && params.url!.isNotEmpty;
    }
  }
} 