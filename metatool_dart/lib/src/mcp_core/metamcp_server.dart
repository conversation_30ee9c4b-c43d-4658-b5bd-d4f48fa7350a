import 'package:mcp_dart/mcp_dart.dart';

import '../data/models/tool_model.dart';
import '../data/repositories/mcp_servers_repository.dart';
import '../data/repositories/namespace_mappings_repository.dart';
import '../data/repositories/tools_repository.dart';
import 'mcp_client_factory.dart';
import 'mcp_server_service.dart';
import 'middleware/filter_tools_middleware.dart';
import 'middleware/middleware.dart';
import 'session_manager.dart';
import 'utils/environment_utils.dart';

// Helper record for the createServer return value.
typedef CreatedServer = ({Server server, Future<void> Function() cleanup});

/// A unified MCP server that acts as a proxy to multiple downstream MCP servers.
/// It aggregates tools, prompts, and resources from all connected servers.
class MetaMcpServer {
  final SessionManager _sessionManager;
  final ToolsRepository _toolsRepository;
  final McpServerService _mcpServerService;

  // In-memory mappings for the current session
  final _toolToClient = <String, ConnectedClient>{};
  final _toolToServerUuid = <String, String>{};
  final _promptToClient = <String, ConnectedClient>{};
  final _resourceToClient = <String, ConnectedClient>{};

  MetaMcpServer({
    required SessionManager sessionManager,
    required ToolsRepository toolsRepository,
    required McpServerService mcpServerService,
  })  : _sessionManager = sessionManager,
        _toolsRepository = toolsRepository,
        _mcpServerService = mcpServerService;

  /// Factory method to create and configure the MetaMcpServer.
  static Future<CreatedServer> createServer({
    required String namespaceUuid,
    required String sessionId,
    required SessionManager sessionManager,
    required ToolsRepository toolsRepository,
    required McpServerService mcpServerService,
    required McpServersRepository mcpServersRepo,
    required NamespaceMappingsRepository namespaceMappingsRepo,
    bool includeInactiveServers = false,
  }) async {
    final metaServer = MetaMcpServer(
      sessionManager: sessionManager,
      toolsRepository: toolsRepository,
      mcpServerService: mcpServerService,
    );

    final server = Server(
      Implementation(name: "metamcp-unified", version: "1.0.0"),
      options: ServerOptions(
        capabilities: ServerCapabilities(
          tools: ServerCapabilitiesTools(),
          prompts: ServerCapabilitiesPrompts(),
          resources: ServerCapabilitiesResources(),
        ),
      ),
    );

    // Initialize session connections in the background
    sessionManager.initSessionConnections(sessionId, namespaceUuid);

    final handlerContext = MetaMcpHandlerContext(
      namespaceUuid: namespaceUuid,
      sessionId: sessionId,
    );

    // --- Tool Handlers & Middleware ---

    final listToolsWithMiddleware = compose<ListToolsHandler>(
      [
        createFilterListToolsMiddleware(
          namespaceMappingsRepo: namespaceMappingsRepo,
          mcpServersRepo: mcpServersRepo,
        ),
      ],
      metaServer._createListToolsHandler(handlerContext),
    );

    final callToolWithMiddleware = compose<CallToolHandler>(
      [
        createFilterCallToolMiddleware(
          namespaceMappingsRepo: namespaceMappingsRepo,
          mcpServersRepo: mcpServersRepo,
        ),
      ],
      metaServer._createCallToolHandler(handlerContext),
    );

    // Register tools handlers
    server.setRequestHandler<JsonRpcListToolsRequest>(
      'tools/list',
      (request, extra) => listToolsWithMiddleware(request, handlerContext),
      (id, params, meta) => JsonRpcListToolsRequest.fromJson({
        'id': id,
        if (params != null) 'params': params,
        if (meta != null) '_meta': meta,
      }),
    );

    server.setRequestHandler<JsonRpcCallToolRequest>(
      'tools/call',
      (request, extra) => callToolWithMiddleware(request, handlerContext),
      (id, params, meta) => JsonRpcCallToolRequest.fromJson({
        'id': id,
        'params': params ?? {},
        if (meta != null) '_meta': meta,
      }),
    );

    // Register prompts handlers
    server.setRequestHandler<JsonRpcListPromptsRequest>(
      'prompts/list',
      (request, extra) =>
          metaServer._handleListPrompts(request, handlerContext),
      (id, params, meta) => JsonRpcListPromptsRequest.fromJson({
        'id': id,
        if (params != null) 'params': params,
        if (meta != null) '_meta': meta,
      }),
    );

    server.setRequestHandler<JsonRpcGetPromptRequest>(
      'prompts/get',
      (request, extra) => metaServer._handleGetPrompt(request, handlerContext),
      (id, params, meta) => JsonRpcGetPromptRequest.fromJson({
        'id': id,
        'params': params ?? {},
        if (meta != null) '_meta': meta,
      }),
    );

    // Register resources handlers
    server.setRequestHandler<JsonRpcListResourcesRequest>(
      'resources/list',
      (request, extra) =>
          metaServer._handleListResources(request, handlerContext),
      (id, params, meta) => JsonRpcListResourcesRequest.fromJson({
        'id': id,
        if (params != null) 'params': params,
        if (meta != null) '_meta': meta,
      }),
    );

    server.setRequestHandler<JsonRpcReadResourceRequest>(
      'resources/read',
      (request, extra) =>
          metaServer._handleReadResource(request, handlerContext),
      (id, params, meta) => JsonRpcReadResourceRequest.fromJson({
        'id': id,
        'params': params ?? {},
        if (meta != null) '_meta': meta,
      }),
    );

    server.setRequestHandler<JsonRpcListResourceTemplatesRequest>(
      'resources/templates/list',
      (request, extra) =>
          metaServer._handleListResourceTemplates(request, handlerContext),
      (id, params, meta) => JsonRpcListResourceTemplatesRequest.fromJson({
        'id': id,
        if (params != null) 'params': params,
        if (meta != null) '_meta': meta,
      }),
    );

    // --- Cleanup Logic ---
    final cleanup = () async {
      await sessionManager.cleanupSessionConnections(sessionId);
    };

    return (server: server, cleanup: cleanup);
  }

  // --- Private Handler Creators ---

  ListToolsHandler _createListToolsHandler(MetaMcpHandlerContext context) {
    return (JsonRpcRequest request, MetaMcpHandlerContext context) async {
      final serverParams = await _mcpServerService.getMcpServers(
        context.namespaceUuid,
      );
      final allTools = <Tool>[];

      await Future.wait(serverParams.entries.map((entry) async {
        final mcpServerUuid = entry.key;
        final params = entry.value;

        final session =
            await _sessionManager.getSession(context.sessionId, params);
        if (session == null) return;

        final capabilities = session.client.getServerCapabilities();
        if (capabilities?.tools == null) return;

        final serverName = EnvironmentUtils.sanitizeName(params.name);

        try {
          final result = await session.client.listTools();

          // Save original tools to the database
          if (result.tools.isNotEmpty) {
            try {
              final toolModels = result.tools
                  .map((t) => ToolModel.fromMcpTool(t, mcpServerUuid))
                  .toList();
              await _toolsRepository.bulkUpsert(mcpServerUuid, toolModels);
              print(
                  'Saved ${result.tools.length} tools for server: $serverName');
            } catch (dbError) {
              print(
                  'Error saving tools to database for server $serverName: $dbError');
            }
          }

          // Add server prefix to tool names and cache mappings
          final toolsWithSource = result.tools.map((tool) {
            final toolName = '${serverName}__${tool.name}';
            _toolToClient[toolName] = session;
            _toolToServerUuid[toolName] = mcpServerUuid;
            return tool.copyWith(name: toolName);
          }).toList();

          allTools.addAll(toolsWithSource);
        } catch (e) {
          print('Error fetching tools from $serverName: $e');
        }
      }));

      return ListToolsResult(tools: allTools);
    };
  }

  CallToolHandler _createCallToolHandler(MetaMcpHandlerContext context) {
    return (JsonRpcCallToolRequest request,
        MetaMcpHandlerContext context) async {
      final toolName = request.callParams.name;
      final args = request.callParams.arguments ?? {};

      // Extract the original tool name by removing the server prefix
      final firstDoubleUnderscoreIndex = toolName.indexOf('__');
      if (firstDoubleUnderscoreIndex == -1) {
        throw McpError(
          ErrorCode.invalidParams.value,
          'Invalid tool name format: $toolName',
        );
      }

      final originalToolName =
          toolName.substring(firstDoubleUnderscoreIndex + 2);
      final clientForTool = _toolToClient[toolName];
      final serverUuid = _toolToServerUuid[toolName];

      if (clientForTool == null) {
        throw McpError(
          ErrorCode.methodNotFound.value,
          'Unknown tool: $toolName',
        );
      }

      if (serverUuid == null) {
        throw McpError(
          ErrorCode.internalError.value,
          'Server UUID not found for tool: $toolName',
        );
      }

      try {
        final result = await clientForTool.client.callTool(
          CallToolRequestParams(
            name: originalToolName,
            arguments: args,
          ),
        );

        return result;
      } catch (error) {
        print(
            'Error calling tool "$toolName" through ${clientForTool.client.getServerVersion()?.name ?? "unknown"}: $error');
        rethrow;
      }
    };
  }

  // --- Prompts Handlers ---

  Future<ListPromptsResult> _handleListPrompts(
      JsonRpcListPromptsRequest request, MetaMcpHandlerContext context) async {
    final serverParams =
        await _mcpServerService.getMcpServers(context.namespaceUuid);
    final allPrompts = <Prompt>[];

    await Future.wait(serverParams.entries.map((entry) async {
      final params = entry.value;

      final session =
          await _sessionManager.getSession(context.sessionId, params);
      if (session == null) return;

      final capabilities = session.client.getServerCapabilities();
      if (capabilities?.prompts == null) return;

      final serverName = EnvironmentUtils.sanitizeName(params.name);

      try {
        final result = await session.client.listPrompts();

        if (result.prompts.isNotEmpty) {
          final promptsWithSource = result.prompts.map((prompt) {
            final promptName = '${serverName}__${prompt.name}';
            _promptToClient[promptName] = session;
            return prompt.copyWith(name: promptName);
          }).toList();
          allPrompts.addAll(promptsWithSource);
        }
      } catch (error) {
        print('Error fetching prompts from $serverName: $error');
      }
    }));

    return ListPromptsResult(
      prompts: allPrompts,
      nextCursor: request.listParams.cursor,
    );
  }

  Future<GetPromptResult> _handleGetPrompt(
      JsonRpcGetPromptRequest request, MetaMcpHandlerContext context) async {
    final promptName = request.getParams.name;
    final clientForPrompt = _promptToClient[promptName];

    if (clientForPrompt == null) {
      throw McpError(
        ErrorCode.methodNotFound.value,
        'Unknown prompt: $promptName',
      );
    }

    try {
      // Extract the original prompt name by removing the server prefix
      final firstDoubleUnderscoreIndex = promptName.indexOf('__');
      if (firstDoubleUnderscoreIndex == -1) {
        throw McpError(
          ErrorCode.invalidParams.value,
          'Invalid prompt name format: $promptName',
        );
      }

      final originalPromptName =
          promptName.substring(firstDoubleUnderscoreIndex + 2);
      final response = await clientForPrompt.client.getPrompt(
        GetPromptRequestParams(
          name: originalPromptName,
          arguments: request.getParams.arguments,
        ),
      );

      return response;
    } catch (error) {
      print(
          'Error getting prompt through ${clientForPrompt.client.getServerVersion()?.name}: $error');
      rethrow;
    }
  }

  // --- Resources Handlers ---

  Future<ListResourcesResult> _handleListResources(
      JsonRpcListResourcesRequest request,
      MetaMcpHandlerContext context) async {
    final serverParams =
        await _mcpServerService.getMcpServers(context.namespaceUuid);
    final allResources = <Resource>[];

    await Future.wait(serverParams.entries.map((entry) async {
      final params = entry.value;

      final session =
          await _sessionManager.getSession(context.sessionId, params);
      if (session == null) return;

      final capabilities = session.client.getServerCapabilities();
      if (capabilities?.resources == null) return;

      final serverName = EnvironmentUtils.sanitizeName(params.name);

      try {
        final result = await session.client.listResources();

        if (result.resources.isNotEmpty) {
          final resourcesWithSource = result.resources.map((resource) {
            _resourceToClient[resource.uri] = session;
            return resource;
          }).toList();
          allResources.addAll(resourcesWithSource);
        }
      } catch (error) {
        print('Error fetching resources from $serverName: $error');
      }
    }));

    return ListResourcesResult(
      resources: allResources,
      nextCursor: request.listParams.cursor,
    );
  }

  Future<ReadResourceResult> _handleReadResource(
      JsonRpcReadResourceRequest request, MetaMcpHandlerContext context) async {
    final uri = request.readParams.uri;
    final clientForResource = _resourceToClient[uri];

    if (clientForResource == null) {
      throw McpError(
        ErrorCode.methodNotFound.value,
        'Unknown resource: $uri',
      );
    }

    try {
      return await clientForResource.client.readResource(
        ReadResourceRequestParams(uri: uri),
      );
    } catch (error) {
      print(
          'Error reading resource through ${clientForResource.client.getServerVersion()?.name}: $error');
      rethrow;
    }
  }

  Future<ListResourceTemplatesResult> _handleListResourceTemplates(
      JsonRpcListResourceTemplatesRequest request,
      MetaMcpHandlerContext context) async {
    final serverParams =
        await _mcpServerService.getMcpServers(context.namespaceUuid);
    final allTemplates = <ResourceTemplate>[];

    await Future.wait(serverParams.entries.map((entry) async {
      final params = entry.value;

      final session =
          await _sessionManager.getSession(context.sessionId, params);
      if (session == null) return;

      final capabilities = session.client.getServerCapabilities();
      if (capabilities?.resources == null) return;

      final serverName = EnvironmentUtils.sanitizeName(params.name);

      try {
        // Note: mcp_dart client may not have listResourceTemplates method
        // This would need to be implemented via direct JSON-RPC request
        // For now, return empty list
        final result = await session.client.listResourceTemplates();

        allTemplates.addAll(result.resourceTemplates);
      } catch (error) {
        print('Error fetching resource templates from $serverName: $error');
      }
    }));

    return ListResourceTemplatesResult(
      resourceTemplates: allTemplates,
      nextCursor: request.listParams.cursor,
    );
  }

  // --- Utility Methods ---
  // (No duplicate utility methods - using EnvironmentUtils directly)
}

// Extension to add copyWith methods for MCP types
extension ToolExtension on Tool {
  Tool copyWith(
      {String? name, String? description, ToolInputSchema? inputSchema}) {
    return Tool(
      name: name ?? this.name,
      description: description ?? this.description,
      inputSchema: inputSchema ?? this.inputSchema,
    );
  }
}

extension PromptExtension on Prompt {
  Prompt copyWith(
      {String? name, String? description, List<PromptArgument>? arguments}) {
    return Prompt(
      name: name ?? this.name,
      description: description ?? this.description,
      arguments: arguments ?? this.arguments,
    );
  }
}
