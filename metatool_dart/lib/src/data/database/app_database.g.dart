// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// ignore_for_file: type=lint
class $McpServersTable extends McpServers
    with TableInfo<$McpServersTable, McpServer> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $McpServersTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 255),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  late final GeneratedColumnWithTypeConverter<McpServerType, int> type =
      GeneratedColumn<int>('type', aliasedName, false,
              type: DriftSqlType.int,
              requiredDuringInsert: false,
              defaultValue: const Constant(0))
          .withConverter<McpServerType>($McpServersTable.$convertertype);
  static const VerificationMeta _commandMeta =
      const VerificationMeta('command');
  @override
  late final GeneratedColumn<String> command = GeneratedColumn<String>(
      'command', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  late final GeneratedColumnWithTypeConverter<List<String>?, String> args =
      GeneratedColumn<String>('args', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<List<String>?>($McpServersTable.$converterargsn);
  @override
  late final GeneratedColumnWithTypeConverter<Map<String, String>?, String>
      env = GeneratedColumn<String>('env', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<Map<String, String>?>($McpServersTable.$converterenvn);
  static const VerificationMeta _urlMeta = const VerificationMeta('url');
  @override
  late final GeneratedColumn<String> url = GeneratedColumn<String>(
      'url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _bearerTokenMeta =
      const VerificationMeta('bearerToken');
  @override
  late final GeneratedColumn<String> bearerToken = GeneratedColumn<String>(
      'bearer_token', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        uuid,
        name,
        description,
        type,
        command,
        args,
        env,
        url,
        bearerToken,
        createdAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'mcp_servers';
  @override
  VerificationContext validateIntegrity(Insertable<McpServer> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('command')) {
      context.handle(_commandMeta,
          command.isAcceptableOrUnknown(data['command']!, _commandMeta));
    }
    if (data.containsKey('url')) {
      context.handle(
          _urlMeta, url.isAcceptableOrUnknown(data['url']!, _urlMeta));
    }
    if (data.containsKey('bearer_token')) {
      context.handle(
          _bearerTokenMeta,
          bearerToken.isAcceptableOrUnknown(
              data['bearer_token']!, _bearerTokenMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {name},
      ];
  @override
  McpServer map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return McpServer(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      type: $McpServersTable.$convertertype.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}type'])!),
      command: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}command']),
      args: $McpServersTable.$converterargsn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}args'])),
      env: $McpServersTable.$converterenvn.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}env'])),
      url: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}url']),
      bearerToken: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}bearer_token']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $McpServersTable createAlias(String alias) {
    return $McpServersTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<McpServerType, int, int> $convertertype =
      const EnumIndexConverter<McpServerType>(McpServerType.values);
  static TypeConverter<List<String>, String> $converterargs =
      const StringListConverter();
  static TypeConverter<List<String>?, String?> $converterargsn =
      NullAwareTypeConverter.wrap($converterargs);
  static TypeConverter<Map<String, String>, String> $converterenv =
      const JsonMapConverter();
  static TypeConverter<Map<String, String>?, String?> $converterenvn =
      NullAwareTypeConverter.wrap($converterenv);
}

class McpServer extends DataClass implements Insertable<McpServer> {
  final String uuid;
  final String name;
  final String? description;
  final McpServerType type;
  final String? command;
  final List<String>? args;
  final Map<String, String>? env;
  final String? url;
  final String? bearerToken;
  final DateTime createdAt;
  const McpServer(
      {required this.uuid,
      required this.name,
      this.description,
      required this.type,
      this.command,
      this.args,
      this.env,
      this.url,
      this.bearerToken,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['name'] = Variable<String>(name);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    {
      map['type'] = Variable<int>($McpServersTable.$convertertype.toSql(type));
    }
    if (!nullToAbsent || command != null) {
      map['command'] = Variable<String>(command);
    }
    if (!nullToAbsent || args != null) {
      map['args'] =
          Variable<String>($McpServersTable.$converterargsn.toSql(args));
    }
    if (!nullToAbsent || env != null) {
      map['env'] = Variable<String>($McpServersTable.$converterenvn.toSql(env));
    }
    if (!nullToAbsent || url != null) {
      map['url'] = Variable<String>(url);
    }
    if (!nullToAbsent || bearerToken != null) {
      map['bearer_token'] = Variable<String>(bearerToken);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  McpServersCompanion toCompanion(bool nullToAbsent) {
    return McpServersCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      type: Value(type),
      command: command == null && nullToAbsent
          ? const Value.absent()
          : Value(command),
      args: args == null && nullToAbsent ? const Value.absent() : Value(args),
      env: env == null && nullToAbsent ? const Value.absent() : Value(env),
      url: url == null && nullToAbsent ? const Value.absent() : Value(url),
      bearerToken: bearerToken == null && nullToAbsent
          ? const Value.absent()
          : Value(bearerToken),
      createdAt: Value(createdAt),
    );
  }

  factory McpServer.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return McpServer(
      uuid: serializer.fromJson<String>(json['uuid']),
      name: serializer.fromJson<String>(json['name']),
      description: serializer.fromJson<String?>(json['description']),
      type: $McpServersTable.$convertertype
          .fromJson(serializer.fromJson<int>(json['type'])),
      command: serializer.fromJson<String?>(json['command']),
      args: serializer.fromJson<List<String>?>(json['args']),
      env: serializer.fromJson<Map<String, String>?>(json['env']),
      url: serializer.fromJson<String?>(json['url']),
      bearerToken: serializer.fromJson<String?>(json['bearerToken']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'name': serializer.toJson<String>(name),
      'description': serializer.toJson<String?>(description),
      'type':
          serializer.toJson<int>($McpServersTable.$convertertype.toJson(type)),
      'command': serializer.toJson<String?>(command),
      'args': serializer.toJson<List<String>?>(args),
      'env': serializer.toJson<Map<String, String>?>(env),
      'url': serializer.toJson<String?>(url),
      'bearerToken': serializer.toJson<String?>(bearerToken),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  McpServer copyWith(
          {String? uuid,
          String? name,
          Value<String?> description = const Value.absent(),
          McpServerType? type,
          Value<String?> command = const Value.absent(),
          Value<List<String>?> args = const Value.absent(),
          Value<Map<String, String>?> env = const Value.absent(),
          Value<String?> url = const Value.absent(),
          Value<String?> bearerToken = const Value.absent(),
          DateTime? createdAt}) =>
      McpServer(
        uuid: uuid ?? this.uuid,
        name: name ?? this.name,
        description: description.present ? description.value : this.description,
        type: type ?? this.type,
        command: command.present ? command.value : this.command,
        args: args.present ? args.value : this.args,
        env: env.present ? env.value : this.env,
        url: url.present ? url.value : this.url,
        bearerToken: bearerToken.present ? bearerToken.value : this.bearerToken,
        createdAt: createdAt ?? this.createdAt,
      );
  McpServer copyWithCompanion(McpServersCompanion data) {
    return McpServer(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      name: data.name.present ? data.name.value : this.name,
      description:
          data.description.present ? data.description.value : this.description,
      type: data.type.present ? data.type.value : this.type,
      command: data.command.present ? data.command.value : this.command,
      args: data.args.present ? data.args.value : this.args,
      env: data.env.present ? data.env.value : this.env,
      url: data.url.present ? data.url.value : this.url,
      bearerToken:
          data.bearerToken.present ? data.bearerToken.value : this.bearerToken,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('McpServer(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('type: $type, ')
          ..write('command: $command, ')
          ..write('args: $args, ')
          ..write('env: $env, ')
          ..write('url: $url, ')
          ..write('bearerToken: $bearerToken, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(uuid, name, description, type, command, args,
      env, url, bearerToken, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is McpServer &&
          other.uuid == this.uuid &&
          other.name == this.name &&
          other.description == this.description &&
          other.type == this.type &&
          other.command == this.command &&
          other.args == this.args &&
          other.env == this.env &&
          other.url == this.url &&
          other.bearerToken == this.bearerToken &&
          other.createdAt == this.createdAt);
}

class McpServersCompanion extends UpdateCompanion<McpServer> {
  final Value<String> uuid;
  final Value<String> name;
  final Value<String?> description;
  final Value<McpServerType> type;
  final Value<String?> command;
  final Value<List<String>?> args;
  final Value<Map<String, String>?> env;
  final Value<String?> url;
  final Value<String?> bearerToken;
  final Value<DateTime> createdAt;
  final Value<int> rowid;
  const McpServersCompanion({
    this.uuid = const Value.absent(),
    this.name = const Value.absent(),
    this.description = const Value.absent(),
    this.type = const Value.absent(),
    this.command = const Value.absent(),
    this.args = const Value.absent(),
    this.env = const Value.absent(),
    this.url = const Value.absent(),
    this.bearerToken = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  McpServersCompanion.insert({
    required String uuid,
    required String name,
    this.description = const Value.absent(),
    this.type = const Value.absent(),
    this.command = const Value.absent(),
    this.args = const Value.absent(),
    this.env = const Value.absent(),
    this.url = const Value.absent(),
    this.bearerToken = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        name = Value(name);
  static Insertable<McpServer> custom({
    Expression<String>? uuid,
    Expression<String>? name,
    Expression<String>? description,
    Expression<int>? type,
    Expression<String>? command,
    Expression<String>? args,
    Expression<String>? env,
    Expression<String>? url,
    Expression<String>? bearerToken,
    Expression<DateTime>? createdAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (type != null) 'type': type,
      if (command != null) 'command': command,
      if (args != null) 'args': args,
      if (env != null) 'env': env,
      if (url != null) 'url': url,
      if (bearerToken != null) 'bearer_token': bearerToken,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  McpServersCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? name,
      Value<String?>? description,
      Value<McpServerType>? type,
      Value<String?>? command,
      Value<List<String>?>? args,
      Value<Map<String, String>?>? env,
      Value<String?>? url,
      Value<String?>? bearerToken,
      Value<DateTime>? createdAt,
      Value<int>? rowid}) {
    return McpServersCompanion(
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      command: command ?? this.command,
      args: args ?? this.args,
      env: env ?? this.env,
      url: url ?? this.url,
      bearerToken: bearerToken ?? this.bearerToken,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (type.present) {
      map['type'] =
          Variable<int>($McpServersTable.$convertertype.toSql(type.value));
    }
    if (command.present) {
      map['command'] = Variable<String>(command.value);
    }
    if (args.present) {
      map['args'] =
          Variable<String>($McpServersTable.$converterargsn.toSql(args.value));
    }
    if (env.present) {
      map['env'] =
          Variable<String>($McpServersTable.$converterenvn.toSql(env.value));
    }
    if (url.present) {
      map['url'] = Variable<String>(url.value);
    }
    if (bearerToken.present) {
      map['bearer_token'] = Variable<String>(bearerToken.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('McpServersCompanion(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('type: $type, ')
          ..write('command: $command, ')
          ..write('args: $args, ')
          ..write('env: $env, ')
          ..write('url: $url, ')
          ..write('bearerToken: $bearerToken, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $OAuthSessionsTable extends OAuthSessions
    with TableInfo<$OAuthSessionsTable, OAuthSession> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $OAuthSessionsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _mcpServerUuidMeta =
      const VerificationMeta('mcpServerUuid');
  @override
  late final GeneratedColumn<String> mcpServerUuid = GeneratedColumn<String>(
      'mcp_server_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES mcp_servers (uuid) ON DELETE CASCADE'));
  static const VerificationMeta _clientInformationMeta =
      const VerificationMeta('clientInformation');
  @override
  late final GeneratedColumn<String> clientInformation =
      GeneratedColumn<String>('client_information', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _tokensMeta = const VerificationMeta('tokens');
  @override
  late final GeneratedColumn<String> tokens = GeneratedColumn<String>(
      'tokens', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _codeVerifierMeta =
      const VerificationMeta('codeVerifier');
  @override
  late final GeneratedColumn<String> codeVerifier = GeneratedColumn<String>(
      'code_verifier', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        uuid,
        mcpServerUuid,
        clientInformation,
        tokens,
        codeVerifier,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'o_auth_sessions';
  @override
  VerificationContext validateIntegrity(Insertable<OAuthSession> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('mcp_server_uuid')) {
      context.handle(
          _mcpServerUuidMeta,
          mcpServerUuid.isAcceptableOrUnknown(
              data['mcp_server_uuid']!, _mcpServerUuidMeta));
    } else if (isInserting) {
      context.missing(_mcpServerUuidMeta);
    }
    if (data.containsKey('client_information')) {
      context.handle(
          _clientInformationMeta,
          clientInformation.isAcceptableOrUnknown(
              data['client_information']!, _clientInformationMeta));
    }
    if (data.containsKey('tokens')) {
      context.handle(_tokensMeta,
          tokens.isAcceptableOrUnknown(data['tokens']!, _tokensMeta));
    }
    if (data.containsKey('code_verifier')) {
      context.handle(
          _codeVerifierMeta,
          codeVerifier.isAcceptableOrUnknown(
              data['code_verifier']!, _codeVerifierMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {mcpServerUuid},
      ];
  @override
  OAuthSession map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return OAuthSession(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      mcpServerUuid: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}mcp_server_uuid'])!,
      clientInformation: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}client_information']),
      tokens: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tokens']),
      codeVerifier: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}code_verifier']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $OAuthSessionsTable createAlias(String alias) {
    return $OAuthSessionsTable(attachedDatabase, alias);
  }
}

class OAuthSession extends DataClass implements Insertable<OAuthSession> {
  final String uuid;
  final String mcpServerUuid;
  final String? clientInformation;
  final String? tokens;
  final String? codeVerifier;
  final DateTime createdAt;
  final DateTime updatedAt;
  const OAuthSession(
      {required this.uuid,
      required this.mcpServerUuid,
      this.clientInformation,
      this.tokens,
      this.codeVerifier,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['mcp_server_uuid'] = Variable<String>(mcpServerUuid);
    if (!nullToAbsent || clientInformation != null) {
      map['client_information'] = Variable<String>(clientInformation);
    }
    if (!nullToAbsent || tokens != null) {
      map['tokens'] = Variable<String>(tokens);
    }
    if (!nullToAbsent || codeVerifier != null) {
      map['code_verifier'] = Variable<String>(codeVerifier);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  OAuthSessionsCompanion toCompanion(bool nullToAbsent) {
    return OAuthSessionsCompanion(
      uuid: Value(uuid),
      mcpServerUuid: Value(mcpServerUuid),
      clientInformation: clientInformation == null && nullToAbsent
          ? const Value.absent()
          : Value(clientInformation),
      tokens:
          tokens == null && nullToAbsent ? const Value.absent() : Value(tokens),
      codeVerifier: codeVerifier == null && nullToAbsent
          ? const Value.absent()
          : Value(codeVerifier),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory OAuthSession.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return OAuthSession(
      uuid: serializer.fromJson<String>(json['uuid']),
      mcpServerUuid: serializer.fromJson<String>(json['mcpServerUuid']),
      clientInformation:
          serializer.fromJson<String?>(json['clientInformation']),
      tokens: serializer.fromJson<String?>(json['tokens']),
      codeVerifier: serializer.fromJson<String?>(json['codeVerifier']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'mcpServerUuid': serializer.toJson<String>(mcpServerUuid),
      'clientInformation': serializer.toJson<String?>(clientInformation),
      'tokens': serializer.toJson<String?>(tokens),
      'codeVerifier': serializer.toJson<String?>(codeVerifier),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  OAuthSession copyWith(
          {String? uuid,
          String? mcpServerUuid,
          Value<String?> clientInformation = const Value.absent(),
          Value<String?> tokens = const Value.absent(),
          Value<String?> codeVerifier = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      OAuthSession(
        uuid: uuid ?? this.uuid,
        mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
        clientInformation: clientInformation.present
            ? clientInformation.value
            : this.clientInformation,
        tokens: tokens.present ? tokens.value : this.tokens,
        codeVerifier:
            codeVerifier.present ? codeVerifier.value : this.codeVerifier,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  OAuthSession copyWithCompanion(OAuthSessionsCompanion data) {
    return OAuthSession(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      mcpServerUuid: data.mcpServerUuid.present
          ? data.mcpServerUuid.value
          : this.mcpServerUuid,
      clientInformation: data.clientInformation.present
          ? data.clientInformation.value
          : this.clientInformation,
      tokens: data.tokens.present ? data.tokens.value : this.tokens,
      codeVerifier: data.codeVerifier.present
          ? data.codeVerifier.value
          : this.codeVerifier,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('OAuthSession(')
          ..write('uuid: $uuid, ')
          ..write('mcpServerUuid: $mcpServerUuid, ')
          ..write('clientInformation: $clientInformation, ')
          ..write('tokens: $tokens, ')
          ..write('codeVerifier: $codeVerifier, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(uuid, mcpServerUuid, clientInformation,
      tokens, codeVerifier, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is OAuthSession &&
          other.uuid == this.uuid &&
          other.mcpServerUuid == this.mcpServerUuid &&
          other.clientInformation == this.clientInformation &&
          other.tokens == this.tokens &&
          other.codeVerifier == this.codeVerifier &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class OAuthSessionsCompanion extends UpdateCompanion<OAuthSession> {
  final Value<String> uuid;
  final Value<String> mcpServerUuid;
  final Value<String?> clientInformation;
  final Value<String?> tokens;
  final Value<String?> codeVerifier;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const OAuthSessionsCompanion({
    this.uuid = const Value.absent(),
    this.mcpServerUuid = const Value.absent(),
    this.clientInformation = const Value.absent(),
    this.tokens = const Value.absent(),
    this.codeVerifier = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  OAuthSessionsCompanion.insert({
    required String uuid,
    required String mcpServerUuid,
    this.clientInformation = const Value.absent(),
    this.tokens = const Value.absent(),
    this.codeVerifier = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        mcpServerUuid = Value(mcpServerUuid);
  static Insertable<OAuthSession> custom({
    Expression<String>? uuid,
    Expression<String>? mcpServerUuid,
    Expression<String>? clientInformation,
    Expression<String>? tokens,
    Expression<String>? codeVerifier,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (mcpServerUuid != null) 'mcp_server_uuid': mcpServerUuid,
      if (clientInformation != null) 'client_information': clientInformation,
      if (tokens != null) 'tokens': tokens,
      if (codeVerifier != null) 'code_verifier': codeVerifier,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  OAuthSessionsCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? mcpServerUuid,
      Value<String?>? clientInformation,
      Value<String?>? tokens,
      Value<String?>? codeVerifier,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return OAuthSessionsCompanion(
      uuid: uuid ?? this.uuid,
      mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
      clientInformation: clientInformation ?? this.clientInformation,
      tokens: tokens ?? this.tokens,
      codeVerifier: codeVerifier ?? this.codeVerifier,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (mcpServerUuid.present) {
      map['mcp_server_uuid'] = Variable<String>(mcpServerUuid.value);
    }
    if (clientInformation.present) {
      map['client_information'] = Variable<String>(clientInformation.value);
    }
    if (tokens.present) {
      map['tokens'] = Variable<String>(tokens.value);
    }
    if (codeVerifier.present) {
      map['code_verifier'] = Variable<String>(codeVerifier.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('OAuthSessionsCompanion(')
          ..write('uuid: $uuid, ')
          ..write('mcpServerUuid: $mcpServerUuid, ')
          ..write('clientInformation: $clientInformation, ')
          ..write('tokens: $tokens, ')
          ..write('codeVerifier: $codeVerifier, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ToolsTable extends Tools with TableInfo<$ToolsTable, Tool> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ToolsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _toolSchemaMeta =
      const VerificationMeta('toolSchema');
  @override
  late final GeneratedColumn<String> toolSchema = GeneratedColumn<String>(
      'tool_schema', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _mcpServerUuidMeta =
      const VerificationMeta('mcpServerUuid');
  @override
  late final GeneratedColumn<String> mcpServerUuid = GeneratedColumn<String>(
      'mcp_server_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES mcp_servers (uuid) ON DELETE CASCADE'));
  @override
  List<GeneratedColumn> get $columns => [
        uuid,
        name,
        description,
        toolSchema,
        createdAt,
        updatedAt,
        mcpServerUuid
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'tools';
  @override
  VerificationContext validateIntegrity(Insertable<Tool> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('tool_schema')) {
      context.handle(
          _toolSchemaMeta,
          toolSchema.isAcceptableOrUnknown(
              data['tool_schema']!, _toolSchemaMeta));
    } else if (isInserting) {
      context.missing(_toolSchemaMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    if (data.containsKey('mcp_server_uuid')) {
      context.handle(
          _mcpServerUuidMeta,
          mcpServerUuid.isAcceptableOrUnknown(
              data['mcp_server_uuid']!, _mcpServerUuidMeta));
    } else if (isInserting) {
      context.missing(_mcpServerUuidMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {mcpServerUuid, name},
      ];
  @override
  Tool map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Tool(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      toolSchema: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tool_schema'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
      mcpServerUuid: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}mcp_server_uuid'])!,
    );
  }

  @override
  $ToolsTable createAlias(String alias) {
    return $ToolsTable(attachedDatabase, alias);
  }
}

class Tool extends DataClass implements Insertable<Tool> {
  final String uuid;
  final String name;
  final String? description;
  final String toolSchema;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String mcpServerUuid;
  const Tool(
      {required this.uuid,
      required this.name,
      this.description,
      required this.toolSchema,
      required this.createdAt,
      required this.updatedAt,
      required this.mcpServerUuid});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['name'] = Variable<String>(name);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['tool_schema'] = Variable<String>(toolSchema);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    map['mcp_server_uuid'] = Variable<String>(mcpServerUuid);
    return map;
  }

  ToolsCompanion toCompanion(bool nullToAbsent) {
    return ToolsCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      toolSchema: Value(toolSchema),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      mcpServerUuid: Value(mcpServerUuid),
    );
  }

  factory Tool.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Tool(
      uuid: serializer.fromJson<String>(json['uuid']),
      name: serializer.fromJson<String>(json['name']),
      description: serializer.fromJson<String?>(json['description']),
      toolSchema: serializer.fromJson<String>(json['toolSchema']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      mcpServerUuid: serializer.fromJson<String>(json['mcpServerUuid']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'name': serializer.toJson<String>(name),
      'description': serializer.toJson<String?>(description),
      'toolSchema': serializer.toJson<String>(toolSchema),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'mcpServerUuid': serializer.toJson<String>(mcpServerUuid),
    };
  }

  Tool copyWith(
          {String? uuid,
          String? name,
          Value<String?> description = const Value.absent(),
          String? toolSchema,
          DateTime? createdAt,
          DateTime? updatedAt,
          String? mcpServerUuid}) =>
      Tool(
        uuid: uuid ?? this.uuid,
        name: name ?? this.name,
        description: description.present ? description.value : this.description,
        toolSchema: toolSchema ?? this.toolSchema,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
      );
  Tool copyWithCompanion(ToolsCompanion data) {
    return Tool(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      name: data.name.present ? data.name.value : this.name,
      description:
          data.description.present ? data.description.value : this.description,
      toolSchema:
          data.toolSchema.present ? data.toolSchema.value : this.toolSchema,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      mcpServerUuid: data.mcpServerUuid.present
          ? data.mcpServerUuid.value
          : this.mcpServerUuid,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Tool(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('toolSchema: $toolSchema, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('mcpServerUuid: $mcpServerUuid')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      uuid, name, description, toolSchema, createdAt, updatedAt, mcpServerUuid);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Tool &&
          other.uuid == this.uuid &&
          other.name == this.name &&
          other.description == this.description &&
          other.toolSchema == this.toolSchema &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.mcpServerUuid == this.mcpServerUuid);
}

class ToolsCompanion extends UpdateCompanion<Tool> {
  final Value<String> uuid;
  final Value<String> name;
  final Value<String?> description;
  final Value<String> toolSchema;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<String> mcpServerUuid;
  final Value<int> rowid;
  const ToolsCompanion({
    this.uuid = const Value.absent(),
    this.name = const Value.absent(),
    this.description = const Value.absent(),
    this.toolSchema = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.mcpServerUuid = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ToolsCompanion.insert({
    required String uuid,
    required String name,
    this.description = const Value.absent(),
    required String toolSchema,
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    required String mcpServerUuid,
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        name = Value(name),
        toolSchema = Value(toolSchema),
        mcpServerUuid = Value(mcpServerUuid);
  static Insertable<Tool> custom({
    Expression<String>? uuid,
    Expression<String>? name,
    Expression<String>? description,
    Expression<String>? toolSchema,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<String>? mcpServerUuid,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (toolSchema != null) 'tool_schema': toolSchema,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (mcpServerUuid != null) 'mcp_server_uuid': mcpServerUuid,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ToolsCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? name,
      Value<String?>? description,
      Value<String>? toolSchema,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<String>? mcpServerUuid,
      Value<int>? rowid}) {
    return ToolsCompanion(
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      description: description ?? this.description,
      toolSchema: toolSchema ?? this.toolSchema,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (toolSchema.present) {
      map['tool_schema'] = Variable<String>(toolSchema.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (mcpServerUuid.present) {
      map['mcp_server_uuid'] = Variable<String>(mcpServerUuid.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ToolsCompanion(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('toolSchema: $toolSchema, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('mcpServerUuid: $mcpServerUuid, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $UsersTable extends Users with TableInfo<$UsersTable, User> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UsersTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _emailMeta = const VerificationMeta('email');
  @override
  late final GeneratedColumn<String> email = GeneratedColumn<String>(
      'email', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _emailVerifiedMeta =
      const VerificationMeta('emailVerified');
  @override
  late final GeneratedColumn<bool> emailVerified = GeneratedColumn<bool>(
      'email_verified', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("email_verified" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _imageMeta = const VerificationMeta('image');
  @override
  late final GeneratedColumn<String> image = GeneratedColumn<String>(
      'image', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, name, email, emailVerified, image, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'users';
  @override
  VerificationContext validateIntegrity(Insertable<User> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('email')) {
      context.handle(
          _emailMeta, email.isAcceptableOrUnknown(data['email']!, _emailMeta));
    } else if (isInserting) {
      context.missing(_emailMeta);
    }
    if (data.containsKey('email_verified')) {
      context.handle(
          _emailVerifiedMeta,
          emailVerified.isAcceptableOrUnknown(
              data['email_verified']!, _emailVerifiedMeta));
    }
    if (data.containsKey('image')) {
      context.handle(
          _imageMeta, image.isAcceptableOrUnknown(data['image']!, _imageMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  User map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return User(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      email: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}email'])!,
      emailVerified: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}email_verified'])!,
      image: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}image']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $UsersTable createAlias(String alias) {
    return $UsersTable(attachedDatabase, alias);
  }
}

class User extends DataClass implements Insertable<User> {
  final String id;
  final String name;
  final String email;
  final bool emailVerified;
  final String? image;
  final DateTime createdAt;
  final DateTime updatedAt;
  const User(
      {required this.id,
      required this.name,
      required this.email,
      required this.emailVerified,
      this.image,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['name'] = Variable<String>(name);
    map['email'] = Variable<String>(email);
    map['email_verified'] = Variable<bool>(emailVerified);
    if (!nullToAbsent || image != null) {
      map['image'] = Variable<String>(image);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  UsersCompanion toCompanion(bool nullToAbsent) {
    return UsersCompanion(
      id: Value(id),
      name: Value(name),
      email: Value(email),
      emailVerified: Value(emailVerified),
      image:
          image == null && nullToAbsent ? const Value.absent() : Value(image),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory User.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return User(
      id: serializer.fromJson<String>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      email: serializer.fromJson<String>(json['email']),
      emailVerified: serializer.fromJson<bool>(json['emailVerified']),
      image: serializer.fromJson<String?>(json['image']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'name': serializer.toJson<String>(name),
      'email': serializer.toJson<String>(email),
      'emailVerified': serializer.toJson<bool>(emailVerified),
      'image': serializer.toJson<String?>(image),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  User copyWith(
          {String? id,
          String? name,
          String? email,
          bool? emailVerified,
          Value<String?> image = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      User(
        id: id ?? this.id,
        name: name ?? this.name,
        email: email ?? this.email,
        emailVerified: emailVerified ?? this.emailVerified,
        image: image.present ? image.value : this.image,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  User copyWithCompanion(UsersCompanion data) {
    return User(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      email: data.email.present ? data.email.value : this.email,
      emailVerified: data.emailVerified.present
          ? data.emailVerified.value
          : this.emailVerified,
      image: data.image.present ? data.image.value : this.image,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('User(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('email: $email, ')
          ..write('emailVerified: $emailVerified, ')
          ..write('image: $image, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, name, email, emailVerified, image, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is User &&
          other.id == this.id &&
          other.name == this.name &&
          other.email == this.email &&
          other.emailVerified == this.emailVerified &&
          other.image == this.image &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class UsersCompanion extends UpdateCompanion<User> {
  final Value<String> id;
  final Value<String> name;
  final Value<String> email;
  final Value<bool> emailVerified;
  final Value<String?> image;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const UsersCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.email = const Value.absent(),
    this.emailVerified = const Value.absent(),
    this.image = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  UsersCompanion.insert({
    required String id,
    required String name,
    required String email,
    this.emailVerified = const Value.absent(),
    this.image = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        name = Value(name),
        email = Value(email);
  static Insertable<User> custom({
    Expression<String>? id,
    Expression<String>? name,
    Expression<String>? email,
    Expression<bool>? emailVerified,
    Expression<String>? image,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (email != null) 'email': email,
      if (emailVerified != null) 'email_verified': emailVerified,
      if (image != null) 'image': image,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  UsersCompanion copyWith(
      {Value<String>? id,
      Value<String>? name,
      Value<String>? email,
      Value<bool>? emailVerified,
      Value<String?>? image,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return UsersCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      emailVerified: emailVerified ?? this.emailVerified,
      image: image ?? this.image,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (email.present) {
      map['email'] = Variable<String>(email.value);
    }
    if (emailVerified.present) {
      map['email_verified'] = Variable<bool>(emailVerified.value);
    }
    if (image.present) {
      map['image'] = Variable<String>(image.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UsersCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('email: $email, ')
          ..write('emailVerified: $emailVerified, ')
          ..write('image: $image, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $SessionsTable extends Sessions with TableInfo<$SessionsTable, Session> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SessionsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _expiresAtMeta =
      const VerificationMeta('expiresAt');
  @override
  late final GeneratedColumn<DateTime> expiresAt = GeneratedColumn<DateTime>(
      'expires_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _tokenMeta = const VerificationMeta('token');
  @override
  late final GeneratedColumn<String> token = GeneratedColumn<String>(
      'token', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _ipAddressMeta =
      const VerificationMeta('ipAddress');
  @override
  late final GeneratedColumn<String> ipAddress = GeneratedColumn<String>(
      'ip_address', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _userAgentMeta =
      const VerificationMeta('userAgent');
  @override
  late final GeneratedColumn<String> userAgent = GeneratedColumn<String>(
      'user_agent', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _userIdMeta = const VerificationMeta('userId');
  @override
  late final GeneratedColumn<String> userId = GeneratedColumn<String>(
      'user_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES users (id) ON DELETE CASCADE'));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        expiresAt,
        token,
        createdAt,
        updatedAt,
        ipAddress,
        userAgent,
        userId
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'sessions';
  @override
  VerificationContext validateIntegrity(Insertable<Session> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('expires_at')) {
      context.handle(_expiresAtMeta,
          expiresAt.isAcceptableOrUnknown(data['expires_at']!, _expiresAtMeta));
    } else if (isInserting) {
      context.missing(_expiresAtMeta);
    }
    if (data.containsKey('token')) {
      context.handle(
          _tokenMeta, token.isAcceptableOrUnknown(data['token']!, _tokenMeta));
    } else if (isInserting) {
      context.missing(_tokenMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    if (data.containsKey('ip_address')) {
      context.handle(_ipAddressMeta,
          ipAddress.isAcceptableOrUnknown(data['ip_address']!, _ipAddressMeta));
    }
    if (data.containsKey('user_agent')) {
      context.handle(_userAgentMeta,
          userAgent.isAcceptableOrUnknown(data['user_agent']!, _userAgentMeta));
    }
    if (data.containsKey('user_id')) {
      context.handle(_userIdMeta,
          userId.isAcceptableOrUnknown(data['user_id']!, _userIdMeta));
    } else if (isInserting) {
      context.missing(_userIdMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Session map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Session(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      expiresAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}expires_at'])!,
      token: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}token'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
      ipAddress: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ip_address']),
      userAgent: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user_agent']),
      userId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user_id'])!,
    );
  }

  @override
  $SessionsTable createAlias(String alias) {
    return $SessionsTable(attachedDatabase, alias);
  }
}

class Session extends DataClass implements Insertable<Session> {
  final String id;
  final DateTime expiresAt;
  final String token;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? ipAddress;
  final String? userAgent;
  final String userId;
  const Session(
      {required this.id,
      required this.expiresAt,
      required this.token,
      required this.createdAt,
      required this.updatedAt,
      this.ipAddress,
      this.userAgent,
      required this.userId});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['expires_at'] = Variable<DateTime>(expiresAt);
    map['token'] = Variable<String>(token);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    if (!nullToAbsent || ipAddress != null) {
      map['ip_address'] = Variable<String>(ipAddress);
    }
    if (!nullToAbsent || userAgent != null) {
      map['user_agent'] = Variable<String>(userAgent);
    }
    map['user_id'] = Variable<String>(userId);
    return map;
  }

  SessionsCompanion toCompanion(bool nullToAbsent) {
    return SessionsCompanion(
      id: Value(id),
      expiresAt: Value(expiresAt),
      token: Value(token),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      ipAddress: ipAddress == null && nullToAbsent
          ? const Value.absent()
          : Value(ipAddress),
      userAgent: userAgent == null && nullToAbsent
          ? const Value.absent()
          : Value(userAgent),
      userId: Value(userId),
    );
  }

  factory Session.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Session(
      id: serializer.fromJson<String>(json['id']),
      expiresAt: serializer.fromJson<DateTime>(json['expiresAt']),
      token: serializer.fromJson<String>(json['token']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      ipAddress: serializer.fromJson<String?>(json['ipAddress']),
      userAgent: serializer.fromJson<String?>(json['userAgent']),
      userId: serializer.fromJson<String>(json['userId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'expiresAt': serializer.toJson<DateTime>(expiresAt),
      'token': serializer.toJson<String>(token),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'ipAddress': serializer.toJson<String?>(ipAddress),
      'userAgent': serializer.toJson<String?>(userAgent),
      'userId': serializer.toJson<String>(userId),
    };
  }

  Session copyWith(
          {String? id,
          DateTime? expiresAt,
          String? token,
          DateTime? createdAt,
          DateTime? updatedAt,
          Value<String?> ipAddress = const Value.absent(),
          Value<String?> userAgent = const Value.absent(),
          String? userId}) =>
      Session(
        id: id ?? this.id,
        expiresAt: expiresAt ?? this.expiresAt,
        token: token ?? this.token,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        ipAddress: ipAddress.present ? ipAddress.value : this.ipAddress,
        userAgent: userAgent.present ? userAgent.value : this.userAgent,
        userId: userId ?? this.userId,
      );
  Session copyWithCompanion(SessionsCompanion data) {
    return Session(
      id: data.id.present ? data.id.value : this.id,
      expiresAt: data.expiresAt.present ? data.expiresAt.value : this.expiresAt,
      token: data.token.present ? data.token.value : this.token,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      ipAddress: data.ipAddress.present ? data.ipAddress.value : this.ipAddress,
      userAgent: data.userAgent.present ? data.userAgent.value : this.userAgent,
      userId: data.userId.present ? data.userId.value : this.userId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Session(')
          ..write('id: $id, ')
          ..write('expiresAt: $expiresAt, ')
          ..write('token: $token, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('ipAddress: $ipAddress, ')
          ..write('userAgent: $userAgent, ')
          ..write('userId: $userId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id, expiresAt, token, createdAt, updatedAt, ipAddress, userAgent, userId);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Session &&
          other.id == this.id &&
          other.expiresAt == this.expiresAt &&
          other.token == this.token &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.ipAddress == this.ipAddress &&
          other.userAgent == this.userAgent &&
          other.userId == this.userId);
}

class SessionsCompanion extends UpdateCompanion<Session> {
  final Value<String> id;
  final Value<DateTime> expiresAt;
  final Value<String> token;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<String?> ipAddress;
  final Value<String?> userAgent;
  final Value<String> userId;
  final Value<int> rowid;
  const SessionsCompanion({
    this.id = const Value.absent(),
    this.expiresAt = const Value.absent(),
    this.token = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.ipAddress = const Value.absent(),
    this.userAgent = const Value.absent(),
    this.userId = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  SessionsCompanion.insert({
    required String id,
    required DateTime expiresAt,
    required String token,
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.ipAddress = const Value.absent(),
    this.userAgent = const Value.absent(),
    required String userId,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        expiresAt = Value(expiresAt),
        token = Value(token),
        userId = Value(userId);
  static Insertable<Session> custom({
    Expression<String>? id,
    Expression<DateTime>? expiresAt,
    Expression<String>? token,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<String>? ipAddress,
    Expression<String>? userAgent,
    Expression<String>? userId,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (expiresAt != null) 'expires_at': expiresAt,
      if (token != null) 'token': token,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (ipAddress != null) 'ip_address': ipAddress,
      if (userAgent != null) 'user_agent': userAgent,
      if (userId != null) 'user_id': userId,
      if (rowid != null) 'rowid': rowid,
    });
  }

  SessionsCompanion copyWith(
      {Value<String>? id,
      Value<DateTime>? expiresAt,
      Value<String>? token,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<String?>? ipAddress,
      Value<String?>? userAgent,
      Value<String>? userId,
      Value<int>? rowid}) {
    return SessionsCompanion(
      id: id ?? this.id,
      expiresAt: expiresAt ?? this.expiresAt,
      token: token ?? this.token,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      userId: userId ?? this.userId,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (expiresAt.present) {
      map['expires_at'] = Variable<DateTime>(expiresAt.value);
    }
    if (token.present) {
      map['token'] = Variable<String>(token.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (ipAddress.present) {
      map['ip_address'] = Variable<String>(ipAddress.value);
    }
    if (userAgent.present) {
      map['user_agent'] = Variable<String>(userAgent.value);
    }
    if (userId.present) {
      map['user_id'] = Variable<String>(userId.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SessionsCompanion(')
          ..write('id: $id, ')
          ..write('expiresAt: $expiresAt, ')
          ..write('token: $token, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('ipAddress: $ipAddress, ')
          ..write('userAgent: $userAgent, ')
          ..write('userId: $userId, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AccountsTable extends Accounts with TableInfo<$AccountsTable, Account> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AccountsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _accountIdMeta =
      const VerificationMeta('accountId');
  @override
  late final GeneratedColumn<String> accountId = GeneratedColumn<String>(
      'account_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _providerIdMeta =
      const VerificationMeta('providerId');
  @override
  late final GeneratedColumn<String> providerId = GeneratedColumn<String>(
      'provider_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _userIdMeta = const VerificationMeta('userId');
  @override
  late final GeneratedColumn<String> userId = GeneratedColumn<String>(
      'user_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES users (id) ON DELETE CASCADE'));
  static const VerificationMeta _accessTokenMeta =
      const VerificationMeta('accessToken');
  @override
  late final GeneratedColumn<String> accessToken = GeneratedColumn<String>(
      'access_token', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _refreshTokenMeta =
      const VerificationMeta('refreshToken');
  @override
  late final GeneratedColumn<String> refreshToken = GeneratedColumn<String>(
      'refresh_token', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _idTokenMeta =
      const VerificationMeta('idToken');
  @override
  late final GeneratedColumn<String> idToken = GeneratedColumn<String>(
      'id_token', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _accessTokenExpiresAtMeta =
      const VerificationMeta('accessTokenExpiresAt');
  @override
  late final GeneratedColumn<DateTime> accessTokenExpiresAt =
      GeneratedColumn<DateTime>('access_token_expires_at', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _refreshTokenExpiresAtMeta =
      const VerificationMeta('refreshTokenExpiresAt');
  @override
  late final GeneratedColumn<DateTime> refreshTokenExpiresAt =
      GeneratedColumn<DateTime>('refresh_token_expires_at', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _scopeMeta = const VerificationMeta('scope');
  @override
  late final GeneratedColumn<String> scope = GeneratedColumn<String>(
      'scope', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _passwordMeta =
      const VerificationMeta('password');
  @override
  late final GeneratedColumn<String> password = GeneratedColumn<String>(
      'password', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        accountId,
        providerId,
        userId,
        accessToken,
        refreshToken,
        idToken,
        accessTokenExpiresAt,
        refreshTokenExpiresAt,
        scope,
        password,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'accounts';
  @override
  VerificationContext validateIntegrity(Insertable<Account> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('account_id')) {
      context.handle(_accountIdMeta,
          accountId.isAcceptableOrUnknown(data['account_id']!, _accountIdMeta));
    } else if (isInserting) {
      context.missing(_accountIdMeta);
    }
    if (data.containsKey('provider_id')) {
      context.handle(
          _providerIdMeta,
          providerId.isAcceptableOrUnknown(
              data['provider_id']!, _providerIdMeta));
    } else if (isInserting) {
      context.missing(_providerIdMeta);
    }
    if (data.containsKey('user_id')) {
      context.handle(_userIdMeta,
          userId.isAcceptableOrUnknown(data['user_id']!, _userIdMeta));
    } else if (isInserting) {
      context.missing(_userIdMeta);
    }
    if (data.containsKey('access_token')) {
      context.handle(
          _accessTokenMeta,
          accessToken.isAcceptableOrUnknown(
              data['access_token']!, _accessTokenMeta));
    }
    if (data.containsKey('refresh_token')) {
      context.handle(
          _refreshTokenMeta,
          refreshToken.isAcceptableOrUnknown(
              data['refresh_token']!, _refreshTokenMeta));
    }
    if (data.containsKey('id_token')) {
      context.handle(_idTokenMeta,
          idToken.isAcceptableOrUnknown(data['id_token']!, _idTokenMeta));
    }
    if (data.containsKey('access_token_expires_at')) {
      context.handle(
          _accessTokenExpiresAtMeta,
          accessTokenExpiresAt.isAcceptableOrUnknown(
              data['access_token_expires_at']!, _accessTokenExpiresAtMeta));
    }
    if (data.containsKey('refresh_token_expires_at')) {
      context.handle(
          _refreshTokenExpiresAtMeta,
          refreshTokenExpiresAt.isAcceptableOrUnknown(
              data['refresh_token_expires_at']!, _refreshTokenExpiresAtMeta));
    }
    if (data.containsKey('scope')) {
      context.handle(
          _scopeMeta, scope.isAcceptableOrUnknown(data['scope']!, _scopeMeta));
    }
    if (data.containsKey('password')) {
      context.handle(_passwordMeta,
          password.isAcceptableOrUnknown(data['password']!, _passwordMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Account map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Account(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      accountId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_id'])!,
      providerId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}provider_id'])!,
      userId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user_id'])!,
      accessToken: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}access_token']),
      refreshToken: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}refresh_token']),
      idToken: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id_token']),
      accessTokenExpiresAt: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}access_token_expires_at']),
      refreshTokenExpiresAt: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}refresh_token_expires_at']),
      scope: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}scope']),
      password: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}password']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $AccountsTable createAlias(String alias) {
    return $AccountsTable(attachedDatabase, alias);
  }
}

class Account extends DataClass implements Insertable<Account> {
  final String id;
  final String accountId;
  final String providerId;
  final String userId;
  final String? accessToken;
  final String? refreshToken;
  final String? idToken;
  final DateTime? accessTokenExpiresAt;
  final DateTime? refreshTokenExpiresAt;
  final String? scope;
  final String? password;
  final DateTime createdAt;
  final DateTime updatedAt;
  const Account(
      {required this.id,
      required this.accountId,
      required this.providerId,
      required this.userId,
      this.accessToken,
      this.refreshToken,
      this.idToken,
      this.accessTokenExpiresAt,
      this.refreshTokenExpiresAt,
      this.scope,
      this.password,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['account_id'] = Variable<String>(accountId);
    map['provider_id'] = Variable<String>(providerId);
    map['user_id'] = Variable<String>(userId);
    if (!nullToAbsent || accessToken != null) {
      map['access_token'] = Variable<String>(accessToken);
    }
    if (!nullToAbsent || refreshToken != null) {
      map['refresh_token'] = Variable<String>(refreshToken);
    }
    if (!nullToAbsent || idToken != null) {
      map['id_token'] = Variable<String>(idToken);
    }
    if (!nullToAbsent || accessTokenExpiresAt != null) {
      map['access_token_expires_at'] = Variable<DateTime>(accessTokenExpiresAt);
    }
    if (!nullToAbsent || refreshTokenExpiresAt != null) {
      map['refresh_token_expires_at'] =
          Variable<DateTime>(refreshTokenExpiresAt);
    }
    if (!nullToAbsent || scope != null) {
      map['scope'] = Variable<String>(scope);
    }
    if (!nullToAbsent || password != null) {
      map['password'] = Variable<String>(password);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  AccountsCompanion toCompanion(bool nullToAbsent) {
    return AccountsCompanion(
      id: Value(id),
      accountId: Value(accountId),
      providerId: Value(providerId),
      userId: Value(userId),
      accessToken: accessToken == null && nullToAbsent
          ? const Value.absent()
          : Value(accessToken),
      refreshToken: refreshToken == null && nullToAbsent
          ? const Value.absent()
          : Value(refreshToken),
      idToken: idToken == null && nullToAbsent
          ? const Value.absent()
          : Value(idToken),
      accessTokenExpiresAt: accessTokenExpiresAt == null && nullToAbsent
          ? const Value.absent()
          : Value(accessTokenExpiresAt),
      refreshTokenExpiresAt: refreshTokenExpiresAt == null && nullToAbsent
          ? const Value.absent()
          : Value(refreshTokenExpiresAt),
      scope:
          scope == null && nullToAbsent ? const Value.absent() : Value(scope),
      password: password == null && nullToAbsent
          ? const Value.absent()
          : Value(password),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory Account.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Account(
      id: serializer.fromJson<String>(json['id']),
      accountId: serializer.fromJson<String>(json['accountId']),
      providerId: serializer.fromJson<String>(json['providerId']),
      userId: serializer.fromJson<String>(json['userId']),
      accessToken: serializer.fromJson<String?>(json['accessToken']),
      refreshToken: serializer.fromJson<String?>(json['refreshToken']),
      idToken: serializer.fromJson<String?>(json['idToken']),
      accessTokenExpiresAt:
          serializer.fromJson<DateTime?>(json['accessTokenExpiresAt']),
      refreshTokenExpiresAt:
          serializer.fromJson<DateTime?>(json['refreshTokenExpiresAt']),
      scope: serializer.fromJson<String?>(json['scope']),
      password: serializer.fromJson<String?>(json['password']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'accountId': serializer.toJson<String>(accountId),
      'providerId': serializer.toJson<String>(providerId),
      'userId': serializer.toJson<String>(userId),
      'accessToken': serializer.toJson<String?>(accessToken),
      'refreshToken': serializer.toJson<String?>(refreshToken),
      'idToken': serializer.toJson<String?>(idToken),
      'accessTokenExpiresAt':
          serializer.toJson<DateTime?>(accessTokenExpiresAt),
      'refreshTokenExpiresAt':
          serializer.toJson<DateTime?>(refreshTokenExpiresAt),
      'scope': serializer.toJson<String?>(scope),
      'password': serializer.toJson<String?>(password),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  Account copyWith(
          {String? id,
          String? accountId,
          String? providerId,
          String? userId,
          Value<String?> accessToken = const Value.absent(),
          Value<String?> refreshToken = const Value.absent(),
          Value<String?> idToken = const Value.absent(),
          Value<DateTime?> accessTokenExpiresAt = const Value.absent(),
          Value<DateTime?> refreshTokenExpiresAt = const Value.absent(),
          Value<String?> scope = const Value.absent(),
          Value<String?> password = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      Account(
        id: id ?? this.id,
        accountId: accountId ?? this.accountId,
        providerId: providerId ?? this.providerId,
        userId: userId ?? this.userId,
        accessToken: accessToken.present ? accessToken.value : this.accessToken,
        refreshToken:
            refreshToken.present ? refreshToken.value : this.refreshToken,
        idToken: idToken.present ? idToken.value : this.idToken,
        accessTokenExpiresAt: accessTokenExpiresAt.present
            ? accessTokenExpiresAt.value
            : this.accessTokenExpiresAt,
        refreshTokenExpiresAt: refreshTokenExpiresAt.present
            ? refreshTokenExpiresAt.value
            : this.refreshTokenExpiresAt,
        scope: scope.present ? scope.value : this.scope,
        password: password.present ? password.value : this.password,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  Account copyWithCompanion(AccountsCompanion data) {
    return Account(
      id: data.id.present ? data.id.value : this.id,
      accountId: data.accountId.present ? data.accountId.value : this.accountId,
      providerId:
          data.providerId.present ? data.providerId.value : this.providerId,
      userId: data.userId.present ? data.userId.value : this.userId,
      accessToken:
          data.accessToken.present ? data.accessToken.value : this.accessToken,
      refreshToken: data.refreshToken.present
          ? data.refreshToken.value
          : this.refreshToken,
      idToken: data.idToken.present ? data.idToken.value : this.idToken,
      accessTokenExpiresAt: data.accessTokenExpiresAt.present
          ? data.accessTokenExpiresAt.value
          : this.accessTokenExpiresAt,
      refreshTokenExpiresAt: data.refreshTokenExpiresAt.present
          ? data.refreshTokenExpiresAt.value
          : this.refreshTokenExpiresAt,
      scope: data.scope.present ? data.scope.value : this.scope,
      password: data.password.present ? data.password.value : this.password,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Account(')
          ..write('id: $id, ')
          ..write('accountId: $accountId, ')
          ..write('providerId: $providerId, ')
          ..write('userId: $userId, ')
          ..write('accessToken: $accessToken, ')
          ..write('refreshToken: $refreshToken, ')
          ..write('idToken: $idToken, ')
          ..write('accessTokenExpiresAt: $accessTokenExpiresAt, ')
          ..write('refreshTokenExpiresAt: $refreshTokenExpiresAt, ')
          ..write('scope: $scope, ')
          ..write('password: $password, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      accountId,
      providerId,
      userId,
      accessToken,
      refreshToken,
      idToken,
      accessTokenExpiresAt,
      refreshTokenExpiresAt,
      scope,
      password,
      createdAt,
      updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Account &&
          other.id == this.id &&
          other.accountId == this.accountId &&
          other.providerId == this.providerId &&
          other.userId == this.userId &&
          other.accessToken == this.accessToken &&
          other.refreshToken == this.refreshToken &&
          other.idToken == this.idToken &&
          other.accessTokenExpiresAt == this.accessTokenExpiresAt &&
          other.refreshTokenExpiresAt == this.refreshTokenExpiresAt &&
          other.scope == this.scope &&
          other.password == this.password &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class AccountsCompanion extends UpdateCompanion<Account> {
  final Value<String> id;
  final Value<String> accountId;
  final Value<String> providerId;
  final Value<String> userId;
  final Value<String?> accessToken;
  final Value<String?> refreshToken;
  final Value<String?> idToken;
  final Value<DateTime?> accessTokenExpiresAt;
  final Value<DateTime?> refreshTokenExpiresAt;
  final Value<String?> scope;
  final Value<String?> password;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const AccountsCompanion({
    this.id = const Value.absent(),
    this.accountId = const Value.absent(),
    this.providerId = const Value.absent(),
    this.userId = const Value.absent(),
    this.accessToken = const Value.absent(),
    this.refreshToken = const Value.absent(),
    this.idToken = const Value.absent(),
    this.accessTokenExpiresAt = const Value.absent(),
    this.refreshTokenExpiresAt = const Value.absent(),
    this.scope = const Value.absent(),
    this.password = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AccountsCompanion.insert({
    required String id,
    required String accountId,
    required String providerId,
    required String userId,
    this.accessToken = const Value.absent(),
    this.refreshToken = const Value.absent(),
    this.idToken = const Value.absent(),
    this.accessTokenExpiresAt = const Value.absent(),
    this.refreshTokenExpiresAt = const Value.absent(),
    this.scope = const Value.absent(),
    this.password = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        accountId = Value(accountId),
        providerId = Value(providerId),
        userId = Value(userId);
  static Insertable<Account> custom({
    Expression<String>? id,
    Expression<String>? accountId,
    Expression<String>? providerId,
    Expression<String>? userId,
    Expression<String>? accessToken,
    Expression<String>? refreshToken,
    Expression<String>? idToken,
    Expression<DateTime>? accessTokenExpiresAt,
    Expression<DateTime>? refreshTokenExpiresAt,
    Expression<String>? scope,
    Expression<String>? password,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (accountId != null) 'account_id': accountId,
      if (providerId != null) 'provider_id': providerId,
      if (userId != null) 'user_id': userId,
      if (accessToken != null) 'access_token': accessToken,
      if (refreshToken != null) 'refresh_token': refreshToken,
      if (idToken != null) 'id_token': idToken,
      if (accessTokenExpiresAt != null)
        'access_token_expires_at': accessTokenExpiresAt,
      if (refreshTokenExpiresAt != null)
        'refresh_token_expires_at': refreshTokenExpiresAt,
      if (scope != null) 'scope': scope,
      if (password != null) 'password': password,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AccountsCompanion copyWith(
      {Value<String>? id,
      Value<String>? accountId,
      Value<String>? providerId,
      Value<String>? userId,
      Value<String?>? accessToken,
      Value<String?>? refreshToken,
      Value<String?>? idToken,
      Value<DateTime?>? accessTokenExpiresAt,
      Value<DateTime?>? refreshTokenExpiresAt,
      Value<String?>? scope,
      Value<String?>? password,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return AccountsCompanion(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      providerId: providerId ?? this.providerId,
      userId: userId ?? this.userId,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      idToken: idToken ?? this.idToken,
      accessTokenExpiresAt: accessTokenExpiresAt ?? this.accessTokenExpiresAt,
      refreshTokenExpiresAt:
          refreshTokenExpiresAt ?? this.refreshTokenExpiresAt,
      scope: scope ?? this.scope,
      password: password ?? this.password,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (accountId.present) {
      map['account_id'] = Variable<String>(accountId.value);
    }
    if (providerId.present) {
      map['provider_id'] = Variable<String>(providerId.value);
    }
    if (userId.present) {
      map['user_id'] = Variable<String>(userId.value);
    }
    if (accessToken.present) {
      map['access_token'] = Variable<String>(accessToken.value);
    }
    if (refreshToken.present) {
      map['refresh_token'] = Variable<String>(refreshToken.value);
    }
    if (idToken.present) {
      map['id_token'] = Variable<String>(idToken.value);
    }
    if (accessTokenExpiresAt.present) {
      map['access_token_expires_at'] =
          Variable<DateTime>(accessTokenExpiresAt.value);
    }
    if (refreshTokenExpiresAt.present) {
      map['refresh_token_expires_at'] =
          Variable<DateTime>(refreshTokenExpiresAt.value);
    }
    if (scope.present) {
      map['scope'] = Variable<String>(scope.value);
    }
    if (password.present) {
      map['password'] = Variable<String>(password.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AccountsCompanion(')
          ..write('id: $id, ')
          ..write('accountId: $accountId, ')
          ..write('providerId: $providerId, ')
          ..write('userId: $userId, ')
          ..write('accessToken: $accessToken, ')
          ..write('refreshToken: $refreshToken, ')
          ..write('idToken: $idToken, ')
          ..write('accessTokenExpiresAt: $accessTokenExpiresAt, ')
          ..write('refreshTokenExpiresAt: $refreshTokenExpiresAt, ')
          ..write('scope: $scope, ')
          ..write('password: $password, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $VerificationsTable extends Verifications
    with TableInfo<$VerificationsTable, Verification> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $VerificationsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _identifierMeta =
      const VerificationMeta('identifier');
  @override
  late final GeneratedColumn<String> identifier = GeneratedColumn<String>(
      'identifier', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<String> value = GeneratedColumn<String>(
      'value', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _expiresAtMeta =
      const VerificationMeta('expiresAt');
  @override
  late final GeneratedColumn<DateTime> expiresAt = GeneratedColumn<DateTime>(
      'expires_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, identifier, value, expiresAt, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'verifications';
  @override
  VerificationContext validateIntegrity(Insertable<Verification> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('identifier')) {
      context.handle(
          _identifierMeta,
          identifier.isAcceptableOrUnknown(
              data['identifier']!, _identifierMeta));
    } else if (isInserting) {
      context.missing(_identifierMeta);
    }
    if (data.containsKey('value')) {
      context.handle(
          _valueMeta, value.isAcceptableOrUnknown(data['value']!, _valueMeta));
    } else if (isInserting) {
      context.missing(_valueMeta);
    }
    if (data.containsKey('expires_at')) {
      context.handle(_expiresAtMeta,
          expiresAt.isAcceptableOrUnknown(data['expires_at']!, _expiresAtMeta));
    } else if (isInserting) {
      context.missing(_expiresAtMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Verification map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Verification(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      identifier: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}identifier'])!,
      value: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value'])!,
      expiresAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}expires_at'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $VerificationsTable createAlias(String alias) {
    return $VerificationsTable(attachedDatabase, alias);
  }
}

class Verification extends DataClass implements Insertable<Verification> {
  final String id;
  final String identifier;
  final String value;
  final DateTime expiresAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  const Verification(
      {required this.id,
      required this.identifier,
      required this.value,
      required this.expiresAt,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['identifier'] = Variable<String>(identifier);
    map['value'] = Variable<String>(value);
    map['expires_at'] = Variable<DateTime>(expiresAt);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  VerificationsCompanion toCompanion(bool nullToAbsent) {
    return VerificationsCompanion(
      id: Value(id),
      identifier: Value(identifier),
      value: Value(value),
      expiresAt: Value(expiresAt),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory Verification.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Verification(
      id: serializer.fromJson<String>(json['id']),
      identifier: serializer.fromJson<String>(json['identifier']),
      value: serializer.fromJson<String>(json['value']),
      expiresAt: serializer.fromJson<DateTime>(json['expiresAt']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'identifier': serializer.toJson<String>(identifier),
      'value': serializer.toJson<String>(value),
      'expiresAt': serializer.toJson<DateTime>(expiresAt),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  Verification copyWith(
          {String? id,
          String? identifier,
          String? value,
          DateTime? expiresAt,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      Verification(
        id: id ?? this.id,
        identifier: identifier ?? this.identifier,
        value: value ?? this.value,
        expiresAt: expiresAt ?? this.expiresAt,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  Verification copyWithCompanion(VerificationsCompanion data) {
    return Verification(
      id: data.id.present ? data.id.value : this.id,
      identifier:
          data.identifier.present ? data.identifier.value : this.identifier,
      value: data.value.present ? data.value.value : this.value,
      expiresAt: data.expiresAt.present ? data.expiresAt.value : this.expiresAt,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Verification(')
          ..write('id: $id, ')
          ..write('identifier: $identifier, ')
          ..write('value: $value, ')
          ..write('expiresAt: $expiresAt, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, identifier, value, expiresAt, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Verification &&
          other.id == this.id &&
          other.identifier == this.identifier &&
          other.value == this.value &&
          other.expiresAt == this.expiresAt &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class VerificationsCompanion extends UpdateCompanion<Verification> {
  final Value<String> id;
  final Value<String> identifier;
  final Value<String> value;
  final Value<DateTime> expiresAt;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const VerificationsCompanion({
    this.id = const Value.absent(),
    this.identifier = const Value.absent(),
    this.value = const Value.absent(),
    this.expiresAt = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  VerificationsCompanion.insert({
    required String id,
    required String identifier,
    required String value,
    required DateTime expiresAt,
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        identifier = Value(identifier),
        value = Value(value),
        expiresAt = Value(expiresAt);
  static Insertable<Verification> custom({
    Expression<String>? id,
    Expression<String>? identifier,
    Expression<String>? value,
    Expression<DateTime>? expiresAt,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (identifier != null) 'identifier': identifier,
      if (value != null) 'value': value,
      if (expiresAt != null) 'expires_at': expiresAt,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  VerificationsCompanion copyWith(
      {Value<String>? id,
      Value<String>? identifier,
      Value<String>? value,
      Value<DateTime>? expiresAt,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return VerificationsCompanion(
      id: id ?? this.id,
      identifier: identifier ?? this.identifier,
      value: value ?? this.value,
      expiresAt: expiresAt ?? this.expiresAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (identifier.present) {
      map['identifier'] = Variable<String>(identifier.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(value.value);
    }
    if (expiresAt.present) {
      map['expires_at'] = Variable<DateTime>(expiresAt.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('VerificationsCompanion(')
          ..write('id: $id, ')
          ..write('identifier: $identifier, ')
          ..write('value: $value, ')
          ..write('expiresAt: $expiresAt, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $NamespacesTable extends Namespaces
    with TableInfo<$NamespacesTable, Namespace> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $NamespacesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [uuid, name, description, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'namespaces';
  @override
  VerificationContext validateIntegrity(Insertable<Namespace> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  Namespace map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Namespace(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $NamespacesTable createAlias(String alias) {
    return $NamespacesTable(attachedDatabase, alias);
  }
}

class Namespace extends DataClass implements Insertable<Namespace> {
  final String uuid;
  final String name;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  const Namespace(
      {required this.uuid,
      required this.name,
      this.description,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['name'] = Variable<String>(name);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  NamespacesCompanion toCompanion(bool nullToAbsent) {
    return NamespacesCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory Namespace.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Namespace(
      uuid: serializer.fromJson<String>(json['uuid']),
      name: serializer.fromJson<String>(json['name']),
      description: serializer.fromJson<String?>(json['description']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'name': serializer.toJson<String>(name),
      'description': serializer.toJson<String?>(description),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  Namespace copyWith(
          {String? uuid,
          String? name,
          Value<String?> description = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      Namespace(
        uuid: uuid ?? this.uuid,
        name: name ?? this.name,
        description: description.present ? description.value : this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  Namespace copyWithCompanion(NamespacesCompanion data) {
    return Namespace(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      name: data.name.present ? data.name.value : this.name,
      description:
          data.description.present ? data.description.value : this.description,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Namespace(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(uuid, name, description, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Namespace &&
          other.uuid == this.uuid &&
          other.name == this.name &&
          other.description == this.description &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class NamespacesCompanion extends UpdateCompanion<Namespace> {
  final Value<String> uuid;
  final Value<String> name;
  final Value<String?> description;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const NamespacesCompanion({
    this.uuid = const Value.absent(),
    this.name = const Value.absent(),
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  NamespacesCompanion.insert({
    required String uuid,
    required String name,
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        name = Value(name);
  static Insertable<Namespace> custom({
    Expression<String>? uuid,
    Expression<String>? name,
    Expression<String>? description,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  NamespacesCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? name,
      Value<String?>? description,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return NamespacesCompanion(
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('NamespacesCompanion(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $EndpointsTable extends Endpoints
    with TableInfo<$EndpointsTable, Endpoint> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $EndpointsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _namespaceUuidMeta =
      const VerificationMeta('namespaceUuid');
  @override
  late final GeneratedColumn<String> namespaceUuid = GeneratedColumn<String>(
      'namespace_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES namespaces (uuid) ON DELETE CASCADE'));
  static const VerificationMeta _enableApiKeyAuthMeta =
      const VerificationMeta('enableApiKeyAuth');
  @override
  late final GeneratedColumn<bool> enableApiKeyAuth = GeneratedColumn<bool>(
      'enable_api_key_auth', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("enable_api_key_auth" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _useQueryParamAuthMeta =
      const VerificationMeta('useQueryParamAuth');
  @override
  late final GeneratedColumn<bool> useQueryParamAuth = GeneratedColumn<bool>(
      'use_query_param_auth', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("use_query_param_auth" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        uuid,
        name,
        description,
        namespaceUuid,
        enableApiKeyAuth,
        useQueryParamAuth,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'endpoints';
  @override
  VerificationContext validateIntegrity(Insertable<Endpoint> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('namespace_uuid')) {
      context.handle(
          _namespaceUuidMeta,
          namespaceUuid.isAcceptableOrUnknown(
              data['namespace_uuid']!, _namespaceUuidMeta));
    } else if (isInserting) {
      context.missing(_namespaceUuidMeta);
    }
    if (data.containsKey('enable_api_key_auth')) {
      context.handle(
          _enableApiKeyAuthMeta,
          enableApiKeyAuth.isAcceptableOrUnknown(
              data['enable_api_key_auth']!, _enableApiKeyAuthMeta));
    }
    if (data.containsKey('use_query_param_auth')) {
      context.handle(
          _useQueryParamAuthMeta,
          useQueryParamAuth.isAcceptableOrUnknown(
              data['use_query_param_auth']!, _useQueryParamAuthMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  Endpoint map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Endpoint(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      namespaceUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}namespace_uuid'])!,
      enableApiKeyAuth: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}enable_api_key_auth'])!,
      useQueryParamAuth: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}use_query_param_auth'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $EndpointsTable createAlias(String alias) {
    return $EndpointsTable(attachedDatabase, alias);
  }
}

class Endpoint extends DataClass implements Insertable<Endpoint> {
  final String uuid;
  final String name;
  final String? description;
  final String namespaceUuid;
  final bool enableApiKeyAuth;
  final bool useQueryParamAuth;
  final DateTime createdAt;
  final DateTime updatedAt;
  const Endpoint(
      {required this.uuid,
      required this.name,
      this.description,
      required this.namespaceUuid,
      required this.enableApiKeyAuth,
      required this.useQueryParamAuth,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['name'] = Variable<String>(name);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['namespace_uuid'] = Variable<String>(namespaceUuid);
    map['enable_api_key_auth'] = Variable<bool>(enableApiKeyAuth);
    map['use_query_param_auth'] = Variable<bool>(useQueryParamAuth);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  EndpointsCompanion toCompanion(bool nullToAbsent) {
    return EndpointsCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      namespaceUuid: Value(namespaceUuid),
      enableApiKeyAuth: Value(enableApiKeyAuth),
      useQueryParamAuth: Value(useQueryParamAuth),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory Endpoint.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Endpoint(
      uuid: serializer.fromJson<String>(json['uuid']),
      name: serializer.fromJson<String>(json['name']),
      description: serializer.fromJson<String?>(json['description']),
      namespaceUuid: serializer.fromJson<String>(json['namespaceUuid']),
      enableApiKeyAuth: serializer.fromJson<bool>(json['enableApiKeyAuth']),
      useQueryParamAuth: serializer.fromJson<bool>(json['useQueryParamAuth']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'name': serializer.toJson<String>(name),
      'description': serializer.toJson<String?>(description),
      'namespaceUuid': serializer.toJson<String>(namespaceUuid),
      'enableApiKeyAuth': serializer.toJson<bool>(enableApiKeyAuth),
      'useQueryParamAuth': serializer.toJson<bool>(useQueryParamAuth),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  Endpoint copyWith(
          {String? uuid,
          String? name,
          Value<String?> description = const Value.absent(),
          String? namespaceUuid,
          bool? enableApiKeyAuth,
          bool? useQueryParamAuth,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      Endpoint(
        uuid: uuid ?? this.uuid,
        name: name ?? this.name,
        description: description.present ? description.value : this.description,
        namespaceUuid: namespaceUuid ?? this.namespaceUuid,
        enableApiKeyAuth: enableApiKeyAuth ?? this.enableApiKeyAuth,
        useQueryParamAuth: useQueryParamAuth ?? this.useQueryParamAuth,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  Endpoint copyWithCompanion(EndpointsCompanion data) {
    return Endpoint(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      name: data.name.present ? data.name.value : this.name,
      description:
          data.description.present ? data.description.value : this.description,
      namespaceUuid: data.namespaceUuid.present
          ? data.namespaceUuid.value
          : this.namespaceUuid,
      enableApiKeyAuth: data.enableApiKeyAuth.present
          ? data.enableApiKeyAuth.value
          : this.enableApiKeyAuth,
      useQueryParamAuth: data.useQueryParamAuth.present
          ? data.useQueryParamAuth.value
          : this.useQueryParamAuth,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Endpoint(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('namespaceUuid: $namespaceUuid, ')
          ..write('enableApiKeyAuth: $enableApiKeyAuth, ')
          ..write('useQueryParamAuth: $useQueryParamAuth, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(uuid, name, description, namespaceUuid,
      enableApiKeyAuth, useQueryParamAuth, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Endpoint &&
          other.uuid == this.uuid &&
          other.name == this.name &&
          other.description == this.description &&
          other.namespaceUuid == this.namespaceUuid &&
          other.enableApiKeyAuth == this.enableApiKeyAuth &&
          other.useQueryParamAuth == this.useQueryParamAuth &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class EndpointsCompanion extends UpdateCompanion<Endpoint> {
  final Value<String> uuid;
  final Value<String> name;
  final Value<String?> description;
  final Value<String> namespaceUuid;
  final Value<bool> enableApiKeyAuth;
  final Value<bool> useQueryParamAuth;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const EndpointsCompanion({
    this.uuid = const Value.absent(),
    this.name = const Value.absent(),
    this.description = const Value.absent(),
    this.namespaceUuid = const Value.absent(),
    this.enableApiKeyAuth = const Value.absent(),
    this.useQueryParamAuth = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  EndpointsCompanion.insert({
    required String uuid,
    required String name,
    this.description = const Value.absent(),
    required String namespaceUuid,
    this.enableApiKeyAuth = const Value.absent(),
    this.useQueryParamAuth = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        name = Value(name),
        namespaceUuid = Value(namespaceUuid);
  static Insertable<Endpoint> custom({
    Expression<String>? uuid,
    Expression<String>? name,
    Expression<String>? description,
    Expression<String>? namespaceUuid,
    Expression<bool>? enableApiKeyAuth,
    Expression<bool>? useQueryParamAuth,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (namespaceUuid != null) 'namespace_uuid': namespaceUuid,
      if (enableApiKeyAuth != null) 'enable_api_key_auth': enableApiKeyAuth,
      if (useQueryParamAuth != null) 'use_query_param_auth': useQueryParamAuth,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  EndpointsCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? name,
      Value<String?>? description,
      Value<String>? namespaceUuid,
      Value<bool>? enableApiKeyAuth,
      Value<bool>? useQueryParamAuth,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return EndpointsCompanion(
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      description: description ?? this.description,
      namespaceUuid: namespaceUuid ?? this.namespaceUuid,
      enableApiKeyAuth: enableApiKeyAuth ?? this.enableApiKeyAuth,
      useQueryParamAuth: useQueryParamAuth ?? this.useQueryParamAuth,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (namespaceUuid.present) {
      map['namespace_uuid'] = Variable<String>(namespaceUuid.value);
    }
    if (enableApiKeyAuth.present) {
      map['enable_api_key_auth'] = Variable<bool>(enableApiKeyAuth.value);
    }
    if (useQueryParamAuth.present) {
      map['use_query_param_auth'] = Variable<bool>(useQueryParamAuth.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('EndpointsCompanion(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('description: $description, ')
          ..write('namespaceUuid: $namespaceUuid, ')
          ..write('enableApiKeyAuth: $enableApiKeyAuth, ')
          ..write('useQueryParamAuth: $useQueryParamAuth, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $NamespaceServerMappingsTable extends NamespaceServerMappings
    with TableInfo<$NamespaceServerMappingsTable, NamespaceServerMapping> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $NamespaceServerMappingsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _namespaceUuidMeta =
      const VerificationMeta('namespaceUuid');
  @override
  late final GeneratedColumn<String> namespaceUuid = GeneratedColumn<String>(
      'namespace_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES namespaces (uuid) ON DELETE CASCADE'));
  static const VerificationMeta _mcpServerUuidMeta =
      const VerificationMeta('mcpServerUuid');
  @override
  late final GeneratedColumn<String> mcpServerUuid = GeneratedColumn<String>(
      'mcp_server_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES mcp_servers (uuid) ON DELETE CASCADE'));
  @override
  late final GeneratedColumnWithTypeConverter<McpServerStatus, int> status =
      GeneratedColumn<int>('status', aliasedName, false,
              type: DriftSqlType.int,
              requiredDuringInsert: false,
              defaultValue: const Constant(0))
          .withConverter<McpServerStatus>(
              $NamespaceServerMappingsTable.$converterstatus);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [uuid, namespaceUuid, mcpServerUuid, status, createdAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'namespace_server_mappings';
  @override
  VerificationContext validateIntegrity(
      Insertable<NamespaceServerMapping> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('namespace_uuid')) {
      context.handle(
          _namespaceUuidMeta,
          namespaceUuid.isAcceptableOrUnknown(
              data['namespace_uuid']!, _namespaceUuidMeta));
    } else if (isInserting) {
      context.missing(_namespaceUuidMeta);
    }
    if (data.containsKey('mcp_server_uuid')) {
      context.handle(
          _mcpServerUuidMeta,
          mcpServerUuid.isAcceptableOrUnknown(
              data['mcp_server_uuid']!, _mcpServerUuidMeta));
    } else if (isInserting) {
      context.missing(_mcpServerUuidMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {namespaceUuid, mcpServerUuid},
      ];
  @override
  NamespaceServerMapping map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return NamespaceServerMapping(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      namespaceUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}namespace_uuid'])!,
      mcpServerUuid: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}mcp_server_uuid'])!,
      status: $NamespaceServerMappingsTable.$converterstatus.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.int, data['${effectivePrefix}status'])!),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $NamespaceServerMappingsTable createAlias(String alias) {
    return $NamespaceServerMappingsTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<McpServerStatus, int, int> $converterstatus =
      const EnumIndexConverter<McpServerStatus>(McpServerStatus.values);
}

class NamespaceServerMapping extends DataClass
    implements Insertable<NamespaceServerMapping> {
  final String uuid;
  final String namespaceUuid;
  final String mcpServerUuid;
  final McpServerStatus status;
  final DateTime createdAt;
  const NamespaceServerMapping(
      {required this.uuid,
      required this.namespaceUuid,
      required this.mcpServerUuid,
      required this.status,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['namespace_uuid'] = Variable<String>(namespaceUuid);
    map['mcp_server_uuid'] = Variable<String>(mcpServerUuid);
    {
      map['status'] = Variable<int>(
          $NamespaceServerMappingsTable.$converterstatus.toSql(status));
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  NamespaceServerMappingsCompanion toCompanion(bool nullToAbsent) {
    return NamespaceServerMappingsCompanion(
      uuid: Value(uuid),
      namespaceUuid: Value(namespaceUuid),
      mcpServerUuid: Value(mcpServerUuid),
      status: Value(status),
      createdAt: Value(createdAt),
    );
  }

  factory NamespaceServerMapping.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return NamespaceServerMapping(
      uuid: serializer.fromJson<String>(json['uuid']),
      namespaceUuid: serializer.fromJson<String>(json['namespaceUuid']),
      mcpServerUuid: serializer.fromJson<String>(json['mcpServerUuid']),
      status: $NamespaceServerMappingsTable.$converterstatus
          .fromJson(serializer.fromJson<int>(json['status'])),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'namespaceUuid': serializer.toJson<String>(namespaceUuid),
      'mcpServerUuid': serializer.toJson<String>(mcpServerUuid),
      'status': serializer.toJson<int>(
          $NamespaceServerMappingsTable.$converterstatus.toJson(status)),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  NamespaceServerMapping copyWith(
          {String? uuid,
          String? namespaceUuid,
          String? mcpServerUuid,
          McpServerStatus? status,
          DateTime? createdAt}) =>
      NamespaceServerMapping(
        uuid: uuid ?? this.uuid,
        namespaceUuid: namespaceUuid ?? this.namespaceUuid,
        mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
      );
  NamespaceServerMapping copyWithCompanion(
      NamespaceServerMappingsCompanion data) {
    return NamespaceServerMapping(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      namespaceUuid: data.namespaceUuid.present
          ? data.namespaceUuid.value
          : this.namespaceUuid,
      mcpServerUuid: data.mcpServerUuid.present
          ? data.mcpServerUuid.value
          : this.mcpServerUuid,
      status: data.status.present ? data.status.value : this.status,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('NamespaceServerMapping(')
          ..write('uuid: $uuid, ')
          ..write('namespaceUuid: $namespaceUuid, ')
          ..write('mcpServerUuid: $mcpServerUuid, ')
          ..write('status: $status, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(uuid, namespaceUuid, mcpServerUuid, status, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is NamespaceServerMapping &&
          other.uuid == this.uuid &&
          other.namespaceUuid == this.namespaceUuid &&
          other.mcpServerUuid == this.mcpServerUuid &&
          other.status == this.status &&
          other.createdAt == this.createdAt);
}

class NamespaceServerMappingsCompanion
    extends UpdateCompanion<NamespaceServerMapping> {
  final Value<String> uuid;
  final Value<String> namespaceUuid;
  final Value<String> mcpServerUuid;
  final Value<McpServerStatus> status;
  final Value<DateTime> createdAt;
  final Value<int> rowid;
  const NamespaceServerMappingsCompanion({
    this.uuid = const Value.absent(),
    this.namespaceUuid = const Value.absent(),
    this.mcpServerUuid = const Value.absent(),
    this.status = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  NamespaceServerMappingsCompanion.insert({
    required String uuid,
    required String namespaceUuid,
    required String mcpServerUuid,
    this.status = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        namespaceUuid = Value(namespaceUuid),
        mcpServerUuid = Value(mcpServerUuid);
  static Insertable<NamespaceServerMapping> custom({
    Expression<String>? uuid,
    Expression<String>? namespaceUuid,
    Expression<String>? mcpServerUuid,
    Expression<int>? status,
    Expression<DateTime>? createdAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (namespaceUuid != null) 'namespace_uuid': namespaceUuid,
      if (mcpServerUuid != null) 'mcp_server_uuid': mcpServerUuid,
      if (status != null) 'status': status,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  NamespaceServerMappingsCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? namespaceUuid,
      Value<String>? mcpServerUuid,
      Value<McpServerStatus>? status,
      Value<DateTime>? createdAt,
      Value<int>? rowid}) {
    return NamespaceServerMappingsCompanion(
      uuid: uuid ?? this.uuid,
      namespaceUuid: namespaceUuid ?? this.namespaceUuid,
      mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (namespaceUuid.present) {
      map['namespace_uuid'] = Variable<String>(namespaceUuid.value);
    }
    if (mcpServerUuid.present) {
      map['mcp_server_uuid'] = Variable<String>(mcpServerUuid.value);
    }
    if (status.present) {
      map['status'] = Variable<int>(
          $NamespaceServerMappingsTable.$converterstatus.toSql(status.value));
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('NamespaceServerMappingsCompanion(')
          ..write('uuid: $uuid, ')
          ..write('namespaceUuid: $namespaceUuid, ')
          ..write('mcpServerUuid: $mcpServerUuid, ')
          ..write('status: $status, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $NamespaceToolMappingsTable extends NamespaceToolMappings
    with TableInfo<$NamespaceToolMappingsTable, NamespaceToolMapping> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $NamespaceToolMappingsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _namespaceUuidMeta =
      const VerificationMeta('namespaceUuid');
  @override
  late final GeneratedColumn<String> namespaceUuid = GeneratedColumn<String>(
      'namespace_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES namespaces (uuid) ON DELETE CASCADE'));
  static const VerificationMeta _toolUuidMeta =
      const VerificationMeta('toolUuid');
  @override
  late final GeneratedColumn<String> toolUuid = GeneratedColumn<String>(
      'tool_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES tools (uuid) ON DELETE CASCADE'));
  static const VerificationMeta _mcpServerUuidMeta =
      const VerificationMeta('mcpServerUuid');
  @override
  late final GeneratedColumn<String> mcpServerUuid = GeneratedColumn<String>(
      'mcp_server_uuid', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES mcp_servers (uuid) ON DELETE CASCADE'));
  @override
  late final GeneratedColumnWithTypeConverter<McpServerStatus, int> status =
      GeneratedColumn<int>('status', aliasedName, false,
              type: DriftSqlType.int,
              requiredDuringInsert: false,
              defaultValue: const Constant(0))
          .withConverter<McpServerStatus>(
              $NamespaceToolMappingsTable.$converterstatus);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [uuid, namespaceUuid, toolUuid, mcpServerUuid, status, createdAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'namespace_tool_mappings';
  @override
  VerificationContext validateIntegrity(
      Insertable<NamespaceToolMapping> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('namespace_uuid')) {
      context.handle(
          _namespaceUuidMeta,
          namespaceUuid.isAcceptableOrUnknown(
              data['namespace_uuid']!, _namespaceUuidMeta));
    } else if (isInserting) {
      context.missing(_namespaceUuidMeta);
    }
    if (data.containsKey('tool_uuid')) {
      context.handle(_toolUuidMeta,
          toolUuid.isAcceptableOrUnknown(data['tool_uuid']!, _toolUuidMeta));
    } else if (isInserting) {
      context.missing(_toolUuidMeta);
    }
    if (data.containsKey('mcp_server_uuid')) {
      context.handle(
          _mcpServerUuidMeta,
          mcpServerUuid.isAcceptableOrUnknown(
              data['mcp_server_uuid']!, _mcpServerUuidMeta));
    } else if (isInserting) {
      context.missing(_mcpServerUuidMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {namespaceUuid, toolUuid},
      ];
  @override
  NamespaceToolMapping map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return NamespaceToolMapping(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      namespaceUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}namespace_uuid'])!,
      toolUuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}tool_uuid'])!,
      mcpServerUuid: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}mcp_server_uuid'])!,
      status: $NamespaceToolMappingsTable.$converterstatus.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.int, data['${effectivePrefix}status'])!),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $NamespaceToolMappingsTable createAlias(String alias) {
    return $NamespaceToolMappingsTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<McpServerStatus, int, int> $converterstatus =
      const EnumIndexConverter<McpServerStatus>(McpServerStatus.values);
}

class NamespaceToolMapping extends DataClass
    implements Insertable<NamespaceToolMapping> {
  final String uuid;
  final String namespaceUuid;
  final String toolUuid;
  final String mcpServerUuid;
  final McpServerStatus status;
  final DateTime createdAt;
  const NamespaceToolMapping(
      {required this.uuid,
      required this.namespaceUuid,
      required this.toolUuid,
      required this.mcpServerUuid,
      required this.status,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['namespace_uuid'] = Variable<String>(namespaceUuid);
    map['tool_uuid'] = Variable<String>(toolUuid);
    map['mcp_server_uuid'] = Variable<String>(mcpServerUuid);
    {
      map['status'] = Variable<int>(
          $NamespaceToolMappingsTable.$converterstatus.toSql(status));
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  NamespaceToolMappingsCompanion toCompanion(bool nullToAbsent) {
    return NamespaceToolMappingsCompanion(
      uuid: Value(uuid),
      namespaceUuid: Value(namespaceUuid),
      toolUuid: Value(toolUuid),
      mcpServerUuid: Value(mcpServerUuid),
      status: Value(status),
      createdAt: Value(createdAt),
    );
  }

  factory NamespaceToolMapping.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return NamespaceToolMapping(
      uuid: serializer.fromJson<String>(json['uuid']),
      namespaceUuid: serializer.fromJson<String>(json['namespaceUuid']),
      toolUuid: serializer.fromJson<String>(json['toolUuid']),
      mcpServerUuid: serializer.fromJson<String>(json['mcpServerUuid']),
      status: $NamespaceToolMappingsTable.$converterstatus
          .fromJson(serializer.fromJson<int>(json['status'])),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'namespaceUuid': serializer.toJson<String>(namespaceUuid),
      'toolUuid': serializer.toJson<String>(toolUuid),
      'mcpServerUuid': serializer.toJson<String>(mcpServerUuid),
      'status': serializer.toJson<int>(
          $NamespaceToolMappingsTable.$converterstatus.toJson(status)),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  NamespaceToolMapping copyWith(
          {String? uuid,
          String? namespaceUuid,
          String? toolUuid,
          String? mcpServerUuid,
          McpServerStatus? status,
          DateTime? createdAt}) =>
      NamespaceToolMapping(
        uuid: uuid ?? this.uuid,
        namespaceUuid: namespaceUuid ?? this.namespaceUuid,
        toolUuid: toolUuid ?? this.toolUuid,
        mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
        status: status ?? this.status,
        createdAt: createdAt ?? this.createdAt,
      );
  NamespaceToolMapping copyWithCompanion(NamespaceToolMappingsCompanion data) {
    return NamespaceToolMapping(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      namespaceUuid: data.namespaceUuid.present
          ? data.namespaceUuid.value
          : this.namespaceUuid,
      toolUuid: data.toolUuid.present ? data.toolUuid.value : this.toolUuid,
      mcpServerUuid: data.mcpServerUuid.present
          ? data.mcpServerUuid.value
          : this.mcpServerUuid,
      status: data.status.present ? data.status.value : this.status,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('NamespaceToolMapping(')
          ..write('uuid: $uuid, ')
          ..write('namespaceUuid: $namespaceUuid, ')
          ..write('toolUuid: $toolUuid, ')
          ..write('mcpServerUuid: $mcpServerUuid, ')
          ..write('status: $status, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      uuid, namespaceUuid, toolUuid, mcpServerUuid, status, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is NamespaceToolMapping &&
          other.uuid == this.uuid &&
          other.namespaceUuid == this.namespaceUuid &&
          other.toolUuid == this.toolUuid &&
          other.mcpServerUuid == this.mcpServerUuid &&
          other.status == this.status &&
          other.createdAt == this.createdAt);
}

class NamespaceToolMappingsCompanion
    extends UpdateCompanion<NamespaceToolMapping> {
  final Value<String> uuid;
  final Value<String> namespaceUuid;
  final Value<String> toolUuid;
  final Value<String> mcpServerUuid;
  final Value<McpServerStatus> status;
  final Value<DateTime> createdAt;
  final Value<int> rowid;
  const NamespaceToolMappingsCompanion({
    this.uuid = const Value.absent(),
    this.namespaceUuid = const Value.absent(),
    this.toolUuid = const Value.absent(),
    this.mcpServerUuid = const Value.absent(),
    this.status = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  NamespaceToolMappingsCompanion.insert({
    required String uuid,
    required String namespaceUuid,
    required String toolUuid,
    required String mcpServerUuid,
    this.status = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        namespaceUuid = Value(namespaceUuid),
        toolUuid = Value(toolUuid),
        mcpServerUuid = Value(mcpServerUuid);
  static Insertable<NamespaceToolMapping> custom({
    Expression<String>? uuid,
    Expression<String>? namespaceUuid,
    Expression<String>? toolUuid,
    Expression<String>? mcpServerUuid,
    Expression<int>? status,
    Expression<DateTime>? createdAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (namespaceUuid != null) 'namespace_uuid': namespaceUuid,
      if (toolUuid != null) 'tool_uuid': toolUuid,
      if (mcpServerUuid != null) 'mcp_server_uuid': mcpServerUuid,
      if (status != null) 'status': status,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  NamespaceToolMappingsCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? namespaceUuid,
      Value<String>? toolUuid,
      Value<String>? mcpServerUuid,
      Value<McpServerStatus>? status,
      Value<DateTime>? createdAt,
      Value<int>? rowid}) {
    return NamespaceToolMappingsCompanion(
      uuid: uuid ?? this.uuid,
      namespaceUuid: namespaceUuid ?? this.namespaceUuid,
      toolUuid: toolUuid ?? this.toolUuid,
      mcpServerUuid: mcpServerUuid ?? this.mcpServerUuid,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (namespaceUuid.present) {
      map['namespace_uuid'] = Variable<String>(namespaceUuid.value);
    }
    if (toolUuid.present) {
      map['tool_uuid'] = Variable<String>(toolUuid.value);
    }
    if (mcpServerUuid.present) {
      map['mcp_server_uuid'] = Variable<String>(mcpServerUuid.value);
    }
    if (status.present) {
      map['status'] = Variable<int>(
          $NamespaceToolMappingsTable.$converterstatus.toSql(status.value));
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('NamespaceToolMappingsCompanion(')
          ..write('uuid: $uuid, ')
          ..write('namespaceUuid: $namespaceUuid, ')
          ..write('toolUuid: $toolUuid, ')
          ..write('mcpServerUuid: $mcpServerUuid, ')
          ..write('status: $status, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ApiKeysTable extends ApiKeys with TableInfo<$ApiKeysTable, ApiKey> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ApiKeysTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uuidMeta = const VerificationMeta('uuid');
  @override
  late final GeneratedColumn<String> uuid = GeneratedColumn<String>(
      'uuid', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 36, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _keyMeta = const VerificationMeta('key');
  @override
  late final GeneratedColumn<String> key = GeneratedColumn<String>(
      'key', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _userIdMeta = const VerificationMeta('userId');
  @override
  late final GeneratedColumn<String> userId = GeneratedColumn<String>(
      'user_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES users (id) ON DELETE CASCADE'));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _isActiveMeta =
      const VerificationMeta('isActive');
  @override
  late final GeneratedColumn<bool> isActive = GeneratedColumn<bool>(
      'is_active', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_active" IN (0, 1))'),
      defaultValue: const Constant(true));
  @override
  List<GeneratedColumn> get $columns =>
      [uuid, name, key, userId, createdAt, isActive];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'api_keys';
  @override
  VerificationContext validateIntegrity(Insertable<ApiKey> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uuid')) {
      context.handle(
          _uuidMeta, uuid.isAcceptableOrUnknown(data['uuid']!, _uuidMeta));
    } else if (isInserting) {
      context.missing(_uuidMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('key')) {
      context.handle(
          _keyMeta, key.isAcceptableOrUnknown(data['key']!, _keyMeta));
    } else if (isInserting) {
      context.missing(_keyMeta);
    }
    if (data.containsKey('user_id')) {
      context.handle(_userIdMeta,
          userId.isAcceptableOrUnknown(data['user_id']!, _userIdMeta));
    } else if (isInserting) {
      context.missing(_userIdMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('is_active')) {
      context.handle(_isActiveMeta,
          isActive.isAcceptableOrUnknown(data['is_active']!, _isActiveMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uuid};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {userId, name},
      ];
  @override
  ApiKey map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ApiKey(
      uuid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uuid'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      key: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}key'])!,
      userId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user_id'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      isActive: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_active'])!,
    );
  }

  @override
  $ApiKeysTable createAlias(String alias) {
    return $ApiKeysTable(attachedDatabase, alias);
  }
}

class ApiKey extends DataClass implements Insertable<ApiKey> {
  final String uuid;
  final String name;
  final String key;
  final String userId;
  final DateTime createdAt;
  final bool isActive;
  const ApiKey(
      {required this.uuid,
      required this.name,
      required this.key,
      required this.userId,
      required this.createdAt,
      required this.isActive});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uuid'] = Variable<String>(uuid);
    map['name'] = Variable<String>(name);
    map['key'] = Variable<String>(key);
    map['user_id'] = Variable<String>(userId);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['is_active'] = Variable<bool>(isActive);
    return map;
  }

  ApiKeysCompanion toCompanion(bool nullToAbsent) {
    return ApiKeysCompanion(
      uuid: Value(uuid),
      name: Value(name),
      key: Value(key),
      userId: Value(userId),
      createdAt: Value(createdAt),
      isActive: Value(isActive),
    );
  }

  factory ApiKey.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ApiKey(
      uuid: serializer.fromJson<String>(json['uuid']),
      name: serializer.fromJson<String>(json['name']),
      key: serializer.fromJson<String>(json['key']),
      userId: serializer.fromJson<String>(json['userId']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      isActive: serializer.fromJson<bool>(json['isActive']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uuid': serializer.toJson<String>(uuid),
      'name': serializer.toJson<String>(name),
      'key': serializer.toJson<String>(key),
      'userId': serializer.toJson<String>(userId),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'isActive': serializer.toJson<bool>(isActive),
    };
  }

  ApiKey copyWith(
          {String? uuid,
          String? name,
          String? key,
          String? userId,
          DateTime? createdAt,
          bool? isActive}) =>
      ApiKey(
        uuid: uuid ?? this.uuid,
        name: name ?? this.name,
        key: key ?? this.key,
        userId: userId ?? this.userId,
        createdAt: createdAt ?? this.createdAt,
        isActive: isActive ?? this.isActive,
      );
  ApiKey copyWithCompanion(ApiKeysCompanion data) {
    return ApiKey(
      uuid: data.uuid.present ? data.uuid.value : this.uuid,
      name: data.name.present ? data.name.value : this.name,
      key: data.key.present ? data.key.value : this.key,
      userId: data.userId.present ? data.userId.value : this.userId,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      isActive: data.isActive.present ? data.isActive.value : this.isActive,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ApiKey(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('key: $key, ')
          ..write('userId: $userId, ')
          ..write('createdAt: $createdAt, ')
          ..write('isActive: $isActive')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(uuid, name, key, userId, createdAt, isActive);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ApiKey &&
          other.uuid == this.uuid &&
          other.name == this.name &&
          other.key == this.key &&
          other.userId == this.userId &&
          other.createdAt == this.createdAt &&
          other.isActive == this.isActive);
}

class ApiKeysCompanion extends UpdateCompanion<ApiKey> {
  final Value<String> uuid;
  final Value<String> name;
  final Value<String> key;
  final Value<String> userId;
  final Value<DateTime> createdAt;
  final Value<bool> isActive;
  final Value<int> rowid;
  const ApiKeysCompanion({
    this.uuid = const Value.absent(),
    this.name = const Value.absent(),
    this.key = const Value.absent(),
    this.userId = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.isActive = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ApiKeysCompanion.insert({
    required String uuid,
    required String name,
    required String key,
    required String userId,
    this.createdAt = const Value.absent(),
    this.isActive = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : uuid = Value(uuid),
        name = Value(name),
        key = Value(key),
        userId = Value(userId);
  static Insertable<ApiKey> custom({
    Expression<String>? uuid,
    Expression<String>? name,
    Expression<String>? key,
    Expression<String>? userId,
    Expression<DateTime>? createdAt,
    Expression<bool>? isActive,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uuid != null) 'uuid': uuid,
      if (name != null) 'name': name,
      if (key != null) 'key': key,
      if (userId != null) 'user_id': userId,
      if (createdAt != null) 'created_at': createdAt,
      if (isActive != null) 'is_active': isActive,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ApiKeysCompanion copyWith(
      {Value<String>? uuid,
      Value<String>? name,
      Value<String>? key,
      Value<String>? userId,
      Value<DateTime>? createdAt,
      Value<bool>? isActive,
      Value<int>? rowid}) {
    return ApiKeysCompanion(
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
      key: key ?? this.key,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uuid.present) {
      map['uuid'] = Variable<String>(uuid.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (key.present) {
      map['key'] = Variable<String>(key.value);
    }
    if (userId.present) {
      map['user_id'] = Variable<String>(userId.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (isActive.present) {
      map['is_active'] = Variable<bool>(isActive.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ApiKeysCompanion(')
          ..write('uuid: $uuid, ')
          ..write('name: $name, ')
          ..write('key: $key, ')
          ..write('userId: $userId, ')
          ..write('createdAt: $createdAt, ')
          ..write('isActive: $isActive, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ConfigTable extends Config with TableInfo<$ConfigTable, ConfigEntry> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ConfigTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<String> value = GeneratedColumn<String>(
      'value', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, value, description, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'config';
  @override
  VerificationContext validateIntegrity(Insertable<ConfigEntry> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('value')) {
      context.handle(
          _valueMeta, value.isAcceptableOrUnknown(data['value']!, _valueMeta));
    } else if (isInserting) {
      context.missing(_valueMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ConfigEntry map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ConfigEntry(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      value: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $ConfigTable createAlias(String alias) {
    return $ConfigTable(attachedDatabase, alias);
  }
}

class ConfigEntry extends DataClass implements Insertable<ConfigEntry> {
  final String id;
  final String value;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  const ConfigEntry(
      {required this.id,
      required this.value,
      this.description,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['value'] = Variable<String>(value);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  ConfigCompanion toCompanion(bool nullToAbsent) {
    return ConfigCompanion(
      id: Value(id),
      value: Value(value),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory ConfigEntry.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ConfigEntry(
      id: serializer.fromJson<String>(json['id']),
      value: serializer.fromJson<String>(json['value']),
      description: serializer.fromJson<String?>(json['description']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'value': serializer.toJson<String>(value),
      'description': serializer.toJson<String?>(description),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  ConfigEntry copyWith(
          {String? id,
          String? value,
          Value<String?> description = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      ConfigEntry(
        id: id ?? this.id,
        value: value ?? this.value,
        description: description.present ? description.value : this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  ConfigEntry copyWithCompanion(ConfigCompanion data) {
    return ConfigEntry(
      id: data.id.present ? data.id.value : this.id,
      value: data.value.present ? data.value.value : this.value,
      description:
          data.description.present ? data.description.value : this.description,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ConfigEntry(')
          ..write('id: $id, ')
          ..write('value: $value, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, value, description, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ConfigEntry &&
          other.id == this.id &&
          other.value == this.value &&
          other.description == this.description &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class ConfigCompanion extends UpdateCompanion<ConfigEntry> {
  final Value<String> id;
  final Value<String> value;
  final Value<String?> description;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const ConfigCompanion({
    this.id = const Value.absent(),
    this.value = const Value.absent(),
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ConfigCompanion.insert({
    required String id,
    required String value,
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        value = Value(value);
  static Insertable<ConfigEntry> custom({
    Expression<String>? id,
    Expression<String>? value,
    Expression<String>? description,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (value != null) 'value': value,
      if (description != null) 'description': description,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ConfigCompanion copyWith(
      {Value<String>? id,
      Value<String>? value,
      Value<String?>? description,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return ConfigCompanion(
      id: id ?? this.id,
      value: value ?? this.value,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(value.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ConfigCompanion(')
          ..write('id: $id, ')
          ..write('value: $value, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $McpServersTable mcpServers = $McpServersTable(this);
  late final $OAuthSessionsTable oAuthSessions = $OAuthSessionsTable(this);
  late final $ToolsTable tools = $ToolsTable(this);
  late final $UsersTable users = $UsersTable(this);
  late final $SessionsTable sessions = $SessionsTable(this);
  late final $AccountsTable accounts = $AccountsTable(this);
  late final $VerificationsTable verifications = $VerificationsTable(this);
  late final $NamespacesTable namespaces = $NamespacesTable(this);
  late final $EndpointsTable endpoints = $EndpointsTable(this);
  late final $NamespaceServerMappingsTable namespaceServerMappings =
      $NamespaceServerMappingsTable(this);
  late final $NamespaceToolMappingsTable namespaceToolMappings =
      $NamespaceToolMappingsTable(this);
  late final $ApiKeysTable apiKeys = $ApiKeysTable(this);
  late final $ConfigTable config = $ConfigTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        mcpServers,
        oAuthSessions,
        tools,
        users,
        sessions,
        accounts,
        verifications,
        namespaces,
        endpoints,
        namespaceServerMappings,
        namespaceToolMappings,
        apiKeys,
        config
      ];
  @override
  StreamQueryUpdateRules get streamUpdateRules => const StreamQueryUpdateRules(
        [
          WritePropagation(
            on: TableUpdateQuery.onTableName('mcp_servers',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('o_auth_sessions', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('mcp_servers',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('tools', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('users',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('sessions', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('users',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('accounts', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('namespaces',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('endpoints', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('namespaces',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('namespace_server_mappings', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('mcp_servers',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('namespace_server_mappings', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('namespaces',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('namespace_tool_mappings', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('tools',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('namespace_tool_mappings', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('mcp_servers',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('namespace_tool_mappings', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('users',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('api_keys', kind: UpdateKind.delete),
            ],
          ),
        ],
      );
}

typedef $$McpServersTableCreateCompanionBuilder = McpServersCompanion Function({
  required String uuid,
  required String name,
  Value<String?> description,
  Value<McpServerType> type,
  Value<String?> command,
  Value<List<String>?> args,
  Value<Map<String, String>?> env,
  Value<String?> url,
  Value<String?> bearerToken,
  Value<DateTime> createdAt,
  Value<int> rowid,
});
typedef $$McpServersTableUpdateCompanionBuilder = McpServersCompanion Function({
  Value<String> uuid,
  Value<String> name,
  Value<String?> description,
  Value<McpServerType> type,
  Value<String?> command,
  Value<List<String>?> args,
  Value<Map<String, String>?> env,
  Value<String?> url,
  Value<String?> bearerToken,
  Value<DateTime> createdAt,
  Value<int> rowid,
});

final class $$McpServersTableReferences
    extends BaseReferences<_$AppDatabase, $McpServersTable, McpServer> {
  $$McpServersTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$OAuthSessionsTable, List<OAuthSession>>
      _oAuthSessionsRefsTable(_$AppDatabase db) =>
          MultiTypedResultKey.fromTable(db.oAuthSessions,
              aliasName: $_aliasNameGenerator(
                  db.mcpServers.uuid, db.oAuthSessions.mcpServerUuid));

  $$OAuthSessionsTableProcessedTableManager get oAuthSessionsRefs {
    final manager = $$OAuthSessionsTableTableManager($_db, $_db.oAuthSessions)
        .filter((f) =>
            f.mcpServerUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache = $_typedResult.readTableOrNull(_oAuthSessionsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$ToolsTable, List<Tool>> _toolsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.tools,
          aliasName:
              $_aliasNameGenerator(db.mcpServers.uuid, db.tools.mcpServerUuid));

  $$ToolsTableProcessedTableManager get toolsRefs {
    final manager = $$ToolsTableTableManager($_db, $_db.tools).filter(
        (f) => f.mcpServerUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache = $_typedResult.readTableOrNull(_toolsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$NamespaceServerMappingsTable,
      List<NamespaceServerMapping>> _namespaceServerMappingsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.namespaceServerMappings,
          aliasName: $_aliasNameGenerator(
              db.mcpServers.uuid, db.namespaceServerMappings.mcpServerUuid));

  $$NamespaceServerMappingsTableProcessedTableManager
      get namespaceServerMappingsRefs {
    final manager = $$NamespaceServerMappingsTableTableManager(
            $_db, $_db.namespaceServerMappings)
        .filter((f) =>
            f.mcpServerUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache =
        $_typedResult.readTableOrNull(_namespaceServerMappingsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$NamespaceToolMappingsTable,
      List<NamespaceToolMapping>> _namespaceToolMappingsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.namespaceToolMappings,
          aliasName: $_aliasNameGenerator(
              db.mcpServers.uuid, db.namespaceToolMappings.mcpServerUuid));

  $$NamespaceToolMappingsTableProcessedTableManager
      get namespaceToolMappingsRefs {
    final manager = $$NamespaceToolMappingsTableTableManager(
            $_db, $_db.namespaceToolMappings)
        .filter((f) =>
            f.mcpServerUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache =
        $_typedResult.readTableOrNull(_namespaceToolMappingsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$McpServersTableFilterComposer
    extends Composer<_$AppDatabase, $McpServersTable> {
  $$McpServersTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<McpServerType, McpServerType, int> get type =>
      $composableBuilder(
          column: $table.type,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get command => $composableBuilder(
      column: $table.command, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<List<String>?, List<String>, String>
      get args => $composableBuilder(
          column: $table.args,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnWithTypeConverterFilters<Map<String, String>?, Map<String, String>,
          String>
      get env => $composableBuilder(
          column: $table.env,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get url => $composableBuilder(
      column: $table.url, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bearerToken => $composableBuilder(
      column: $table.bearerToken, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  Expression<bool> oAuthSessionsRefs(
      Expression<bool> Function($$OAuthSessionsTableFilterComposer f) f) {
    final $$OAuthSessionsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.uuid,
        referencedTable: $db.oAuthSessions,
        getReferencedColumn: (t) => t.mcpServerUuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$OAuthSessionsTableFilterComposer(
              $db: $db,
              $table: $db.oAuthSessions,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<bool> toolsRefs(
      Expression<bool> Function($$ToolsTableFilterComposer f) f) {
    final $$ToolsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.uuid,
        referencedTable: $db.tools,
        getReferencedColumn: (t) => t.mcpServerUuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ToolsTableFilterComposer(
              $db: $db,
              $table: $db.tools,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<bool> namespaceServerMappingsRefs(
      Expression<bool> Function($$NamespaceServerMappingsTableFilterComposer f)
          f) {
    final $$NamespaceServerMappingsTableFilterComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceServerMappings,
            getReferencedColumn: (t) => t.mcpServerUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceServerMappingsTableFilterComposer(
                  $db: $db,
                  $table: $db.namespaceServerMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }

  Expression<bool> namespaceToolMappingsRefs(
      Expression<bool> Function($$NamespaceToolMappingsTableFilterComposer f)
          f) {
    final $$NamespaceToolMappingsTableFilterComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceToolMappings,
            getReferencedColumn: (t) => t.mcpServerUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceToolMappingsTableFilterComposer(
                  $db: $db,
                  $table: $db.namespaceToolMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$McpServersTableOrderingComposer
    extends Composer<_$AppDatabase, $McpServersTable> {
  $$McpServersTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get command => $composableBuilder(
      column: $table.command, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get args => $composableBuilder(
      column: $table.args, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get env => $composableBuilder(
      column: $table.env, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get url => $composableBuilder(
      column: $table.url, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bearerToken => $composableBuilder(
      column: $table.bearerToken, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));
}

class $$McpServersTableAnnotationComposer
    extends Composer<_$AppDatabase, $McpServersTable> {
  $$McpServersTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumnWithTypeConverter<McpServerType, int> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<String> get command =>
      $composableBuilder(column: $table.command, builder: (column) => column);

  GeneratedColumnWithTypeConverter<List<String>?, String> get args =>
      $composableBuilder(column: $table.args, builder: (column) => column);

  GeneratedColumnWithTypeConverter<Map<String, String>?, String> get env =>
      $composableBuilder(column: $table.env, builder: (column) => column);

  GeneratedColumn<String> get url =>
      $composableBuilder(column: $table.url, builder: (column) => column);

  GeneratedColumn<String> get bearerToken => $composableBuilder(
      column: $table.bearerToken, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  Expression<T> oAuthSessionsRefs<T extends Object>(
      Expression<T> Function($$OAuthSessionsTableAnnotationComposer a) f) {
    final $$OAuthSessionsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.uuid,
        referencedTable: $db.oAuthSessions,
        getReferencedColumn: (t) => t.mcpServerUuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$OAuthSessionsTableAnnotationComposer(
              $db: $db,
              $table: $db.oAuthSessions,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<T> toolsRefs<T extends Object>(
      Expression<T> Function($$ToolsTableAnnotationComposer a) f) {
    final $$ToolsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.uuid,
        referencedTable: $db.tools,
        getReferencedColumn: (t) => t.mcpServerUuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ToolsTableAnnotationComposer(
              $db: $db,
              $table: $db.tools,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<T> namespaceServerMappingsRefs<T extends Object>(
      Expression<T> Function($$NamespaceServerMappingsTableAnnotationComposer a)
          f) {
    final $$NamespaceServerMappingsTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceServerMappings,
            getReferencedColumn: (t) => t.mcpServerUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceServerMappingsTableAnnotationComposer(
                  $db: $db,
                  $table: $db.namespaceServerMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }

  Expression<T> namespaceToolMappingsRefs<T extends Object>(
      Expression<T> Function($$NamespaceToolMappingsTableAnnotationComposer a)
          f) {
    final $$NamespaceToolMappingsTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceToolMappings,
            getReferencedColumn: (t) => t.mcpServerUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceToolMappingsTableAnnotationComposer(
                  $db: $db,
                  $table: $db.namespaceToolMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$McpServersTableTableManager extends RootTableManager<
    _$AppDatabase,
    $McpServersTable,
    McpServer,
    $$McpServersTableFilterComposer,
    $$McpServersTableOrderingComposer,
    $$McpServersTableAnnotationComposer,
    $$McpServersTableCreateCompanionBuilder,
    $$McpServersTableUpdateCompanionBuilder,
    (McpServer, $$McpServersTableReferences),
    McpServer,
    PrefetchHooks Function(
        {bool oAuthSessionsRefs,
        bool toolsRefs,
        bool namespaceServerMappingsRefs,
        bool namespaceToolMappingsRefs})> {
  $$McpServersTableTableManager(_$AppDatabase db, $McpServersTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$McpServersTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$McpServersTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$McpServersTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<McpServerType> type = const Value.absent(),
            Value<String?> command = const Value.absent(),
            Value<List<String>?> args = const Value.absent(),
            Value<Map<String, String>?> env = const Value.absent(),
            Value<String?> url = const Value.absent(),
            Value<String?> bearerToken = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              McpServersCompanion(
            uuid: uuid,
            name: name,
            description: description,
            type: type,
            command: command,
            args: args,
            env: env,
            url: url,
            bearerToken: bearerToken,
            createdAt: createdAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String name,
            Value<String?> description = const Value.absent(),
            Value<McpServerType> type = const Value.absent(),
            Value<String?> command = const Value.absent(),
            Value<List<String>?> args = const Value.absent(),
            Value<Map<String, String>?> env = const Value.absent(),
            Value<String?> url = const Value.absent(),
            Value<String?> bearerToken = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              McpServersCompanion.insert(
            uuid: uuid,
            name: name,
            description: description,
            type: type,
            command: command,
            args: args,
            env: env,
            url: url,
            bearerToken: bearerToken,
            createdAt: createdAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$McpServersTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: (
              {oAuthSessionsRefs = false,
              toolsRefs = false,
              namespaceServerMappingsRefs = false,
              namespaceToolMappingsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (oAuthSessionsRefs) db.oAuthSessions,
                if (toolsRefs) db.tools,
                if (namespaceServerMappingsRefs) db.namespaceServerMappings,
                if (namespaceToolMappingsRefs) db.namespaceToolMappings
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (oAuthSessionsRefs)
                    await $_getPrefetchedData<McpServer, $McpServersTable,
                            OAuthSession>(
                        currentTable: table,
                        referencedTable: $$McpServersTableReferences
                            ._oAuthSessionsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$McpServersTableReferences(db, table, p0)
                                .oAuthSessionsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.mcpServerUuid == item.uuid),
                        typedResults: items),
                  if (toolsRefs)
                    await $_getPrefetchedData<McpServer, $McpServersTable,
                            Tool>(
                        currentTable: table,
                        referencedTable:
                            $$McpServersTableReferences._toolsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$McpServersTableReferences(db, table, p0)
                                .toolsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.mcpServerUuid == item.uuid),
                        typedResults: items),
                  if (namespaceServerMappingsRefs)
                    await $_getPrefetchedData<McpServer, $McpServersTable,
                            NamespaceServerMapping>(
                        currentTable: table,
                        referencedTable: $$McpServersTableReferences
                            ._namespaceServerMappingsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$McpServersTableReferences(db, table, p0)
                                .namespaceServerMappingsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.mcpServerUuid == item.uuid),
                        typedResults: items),
                  if (namespaceToolMappingsRefs)
                    await $_getPrefetchedData<McpServer, $McpServersTable, NamespaceToolMapping>(
                        currentTable: table,
                        referencedTable: $$McpServersTableReferences
                            ._namespaceToolMappingsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$McpServersTableReferences(db, table, p0)
                                .namespaceToolMappingsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.mcpServerUuid == item.uuid),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$McpServersTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $McpServersTable,
    McpServer,
    $$McpServersTableFilterComposer,
    $$McpServersTableOrderingComposer,
    $$McpServersTableAnnotationComposer,
    $$McpServersTableCreateCompanionBuilder,
    $$McpServersTableUpdateCompanionBuilder,
    (McpServer, $$McpServersTableReferences),
    McpServer,
    PrefetchHooks Function(
        {bool oAuthSessionsRefs,
        bool toolsRefs,
        bool namespaceServerMappingsRefs,
        bool namespaceToolMappingsRefs})>;
typedef $$OAuthSessionsTableCreateCompanionBuilder = OAuthSessionsCompanion
    Function({
  required String uuid,
  required String mcpServerUuid,
  Value<String?> clientInformation,
  Value<String?> tokens,
  Value<String?> codeVerifier,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$OAuthSessionsTableUpdateCompanionBuilder = OAuthSessionsCompanion
    Function({
  Value<String> uuid,
  Value<String> mcpServerUuid,
  Value<String?> clientInformation,
  Value<String?> tokens,
  Value<String?> codeVerifier,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

final class $$OAuthSessionsTableReferences
    extends BaseReferences<_$AppDatabase, $OAuthSessionsTable, OAuthSession> {
  $$OAuthSessionsTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static $McpServersTable _mcpServerUuidTable(_$AppDatabase db) =>
      db.mcpServers.createAlias($_aliasNameGenerator(
          db.oAuthSessions.mcpServerUuid, db.mcpServers.uuid));

  $$McpServersTableProcessedTableManager get mcpServerUuid {
    final $_column = $_itemColumn<String>('mcp_server_uuid')!;

    final manager = $$McpServersTableTableManager($_db, $_db.mcpServers)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_mcpServerUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$OAuthSessionsTableFilterComposer
    extends Composer<_$AppDatabase, $OAuthSessionsTable> {
  $$OAuthSessionsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get clientInformation => $composableBuilder(
      column: $table.clientInformation,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get tokens => $composableBuilder(
      column: $table.tokens, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get codeVerifier => $composableBuilder(
      column: $table.codeVerifier, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  $$McpServersTableFilterComposer get mcpServerUuid {
    final $$McpServersTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableFilterComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$OAuthSessionsTableOrderingComposer
    extends Composer<_$AppDatabase, $OAuthSessionsTable> {
  $$OAuthSessionsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get clientInformation => $composableBuilder(
      column: $table.clientInformation,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get tokens => $composableBuilder(
      column: $table.tokens, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get codeVerifier => $composableBuilder(
      column: $table.codeVerifier,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  $$McpServersTableOrderingComposer get mcpServerUuid {
    final $$McpServersTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableOrderingComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$OAuthSessionsTableAnnotationComposer
    extends Composer<_$AppDatabase, $OAuthSessionsTable> {
  $$OAuthSessionsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<String> get clientInformation => $composableBuilder(
      column: $table.clientInformation, builder: (column) => column);

  GeneratedColumn<String> get tokens =>
      $composableBuilder(column: $table.tokens, builder: (column) => column);

  GeneratedColumn<String> get codeVerifier => $composableBuilder(
      column: $table.codeVerifier, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  $$McpServersTableAnnotationComposer get mcpServerUuid {
    final $$McpServersTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableAnnotationComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$OAuthSessionsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $OAuthSessionsTable,
    OAuthSession,
    $$OAuthSessionsTableFilterComposer,
    $$OAuthSessionsTableOrderingComposer,
    $$OAuthSessionsTableAnnotationComposer,
    $$OAuthSessionsTableCreateCompanionBuilder,
    $$OAuthSessionsTableUpdateCompanionBuilder,
    (OAuthSession, $$OAuthSessionsTableReferences),
    OAuthSession,
    PrefetchHooks Function({bool mcpServerUuid})> {
  $$OAuthSessionsTableTableManager(_$AppDatabase db, $OAuthSessionsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$OAuthSessionsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$OAuthSessionsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$OAuthSessionsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> mcpServerUuid = const Value.absent(),
            Value<String?> clientInformation = const Value.absent(),
            Value<String?> tokens = const Value.absent(),
            Value<String?> codeVerifier = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              OAuthSessionsCompanion(
            uuid: uuid,
            mcpServerUuid: mcpServerUuid,
            clientInformation: clientInformation,
            tokens: tokens,
            codeVerifier: codeVerifier,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String mcpServerUuid,
            Value<String?> clientInformation = const Value.absent(),
            Value<String?> tokens = const Value.absent(),
            Value<String?> codeVerifier = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              OAuthSessionsCompanion.insert(
            uuid: uuid,
            mcpServerUuid: mcpServerUuid,
            clientInformation: clientInformation,
            tokens: tokens,
            codeVerifier: codeVerifier,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$OAuthSessionsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({mcpServerUuid = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (mcpServerUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.mcpServerUuid,
                    referencedTable:
                        $$OAuthSessionsTableReferences._mcpServerUuidTable(db),
                    referencedColumn: $$OAuthSessionsTableReferences
                        ._mcpServerUuidTable(db)
                        .uuid,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$OAuthSessionsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $OAuthSessionsTable,
    OAuthSession,
    $$OAuthSessionsTableFilterComposer,
    $$OAuthSessionsTableOrderingComposer,
    $$OAuthSessionsTableAnnotationComposer,
    $$OAuthSessionsTableCreateCompanionBuilder,
    $$OAuthSessionsTableUpdateCompanionBuilder,
    (OAuthSession, $$OAuthSessionsTableReferences),
    OAuthSession,
    PrefetchHooks Function({bool mcpServerUuid})>;
typedef $$ToolsTableCreateCompanionBuilder = ToolsCompanion Function({
  required String uuid,
  required String name,
  Value<String?> description,
  required String toolSchema,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  required String mcpServerUuid,
  Value<int> rowid,
});
typedef $$ToolsTableUpdateCompanionBuilder = ToolsCompanion Function({
  Value<String> uuid,
  Value<String> name,
  Value<String?> description,
  Value<String> toolSchema,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<String> mcpServerUuid,
  Value<int> rowid,
});

final class $$ToolsTableReferences
    extends BaseReferences<_$AppDatabase, $ToolsTable, Tool> {
  $$ToolsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $McpServersTable _mcpServerUuidTable(_$AppDatabase db) =>
      db.mcpServers.createAlias(
          $_aliasNameGenerator(db.tools.mcpServerUuid, db.mcpServers.uuid));

  $$McpServersTableProcessedTableManager get mcpServerUuid {
    final $_column = $_itemColumn<String>('mcp_server_uuid')!;

    final manager = $$McpServersTableTableManager($_db, $_db.mcpServers)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_mcpServerUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static MultiTypedResultKey<$NamespaceToolMappingsTable,
      List<NamespaceToolMapping>> _namespaceToolMappingsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.namespaceToolMappings,
          aliasName: $_aliasNameGenerator(
              db.tools.uuid, db.namespaceToolMappings.toolUuid));

  $$NamespaceToolMappingsTableProcessedTableManager
      get namespaceToolMappingsRefs {
    final manager = $$NamespaceToolMappingsTableTableManager(
            $_db, $_db.namespaceToolMappings)
        .filter(
            (f) => f.toolUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache =
        $_typedResult.readTableOrNull(_namespaceToolMappingsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$ToolsTableFilterComposer extends Composer<_$AppDatabase, $ToolsTable> {
  $$ToolsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get toolSchema => $composableBuilder(
      column: $table.toolSchema, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  $$McpServersTableFilterComposer get mcpServerUuid {
    final $$McpServersTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableFilterComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  Expression<bool> namespaceToolMappingsRefs(
      Expression<bool> Function($$NamespaceToolMappingsTableFilterComposer f)
          f) {
    final $$NamespaceToolMappingsTableFilterComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceToolMappings,
            getReferencedColumn: (t) => t.toolUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceToolMappingsTableFilterComposer(
                  $db: $db,
                  $table: $db.namespaceToolMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$ToolsTableOrderingComposer
    extends Composer<_$AppDatabase, $ToolsTable> {
  $$ToolsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get toolSchema => $composableBuilder(
      column: $table.toolSchema, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  $$McpServersTableOrderingComposer get mcpServerUuid {
    final $$McpServersTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableOrderingComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$ToolsTableAnnotationComposer
    extends Composer<_$AppDatabase, $ToolsTable> {
  $$ToolsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<String> get toolSchema => $composableBuilder(
      column: $table.toolSchema, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  $$McpServersTableAnnotationComposer get mcpServerUuid {
    final $$McpServersTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableAnnotationComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  Expression<T> namespaceToolMappingsRefs<T extends Object>(
      Expression<T> Function($$NamespaceToolMappingsTableAnnotationComposer a)
          f) {
    final $$NamespaceToolMappingsTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceToolMappings,
            getReferencedColumn: (t) => t.toolUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceToolMappingsTableAnnotationComposer(
                  $db: $db,
                  $table: $db.namespaceToolMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$ToolsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ToolsTable,
    Tool,
    $$ToolsTableFilterComposer,
    $$ToolsTableOrderingComposer,
    $$ToolsTableAnnotationComposer,
    $$ToolsTableCreateCompanionBuilder,
    $$ToolsTableUpdateCompanionBuilder,
    (Tool, $$ToolsTableReferences),
    Tool,
    PrefetchHooks Function(
        {bool mcpServerUuid, bool namespaceToolMappingsRefs})> {
  $$ToolsTableTableManager(_$AppDatabase db, $ToolsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ToolsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ToolsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ToolsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<String> toolSchema = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<String> mcpServerUuid = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ToolsCompanion(
            uuid: uuid,
            name: name,
            description: description,
            toolSchema: toolSchema,
            createdAt: createdAt,
            updatedAt: updatedAt,
            mcpServerUuid: mcpServerUuid,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String name,
            Value<String?> description = const Value.absent(),
            required String toolSchema,
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            required String mcpServerUuid,
            Value<int> rowid = const Value.absent(),
          }) =>
              ToolsCompanion.insert(
            uuid: uuid,
            name: name,
            description: description,
            toolSchema: toolSchema,
            createdAt: createdAt,
            updatedAt: updatedAt,
            mcpServerUuid: mcpServerUuid,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$ToolsTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: (
              {mcpServerUuid = false, namespaceToolMappingsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (namespaceToolMappingsRefs) db.namespaceToolMappings
              ],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (mcpServerUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.mcpServerUuid,
                    referencedTable:
                        $$ToolsTableReferences._mcpServerUuidTable(db),
                    referencedColumn:
                        $$ToolsTableReferences._mcpServerUuidTable(db).uuid,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [
                  if (namespaceToolMappingsRefs)
                    await $_getPrefetchedData<Tool, $ToolsTable,
                            NamespaceToolMapping>(
                        currentTable: table,
                        referencedTable: $$ToolsTableReferences
                            ._namespaceToolMappingsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$ToolsTableReferences(db, table, p0)
                                .namespaceToolMappingsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.toolUuid == item.uuid),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$ToolsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ToolsTable,
    Tool,
    $$ToolsTableFilterComposer,
    $$ToolsTableOrderingComposer,
    $$ToolsTableAnnotationComposer,
    $$ToolsTableCreateCompanionBuilder,
    $$ToolsTableUpdateCompanionBuilder,
    (Tool, $$ToolsTableReferences),
    Tool,
    PrefetchHooks Function(
        {bool mcpServerUuid, bool namespaceToolMappingsRefs})>;
typedef $$UsersTableCreateCompanionBuilder = UsersCompanion Function({
  required String id,
  required String name,
  required String email,
  Value<bool> emailVerified,
  Value<String?> image,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$UsersTableUpdateCompanionBuilder = UsersCompanion Function({
  Value<String> id,
  Value<String> name,
  Value<String> email,
  Value<bool> emailVerified,
  Value<String?> image,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

final class $$UsersTableReferences
    extends BaseReferences<_$AppDatabase, $UsersTable, User> {
  $$UsersTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$SessionsTable, List<Session>> _sessionsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.sessions,
          aliasName: $_aliasNameGenerator(db.users.id, db.sessions.userId));

  $$SessionsTableProcessedTableManager get sessionsRefs {
    final manager = $$SessionsTableTableManager($_db, $_db.sessions)
        .filter((f) => f.userId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_sessionsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$AccountsTable, List<Account>> _accountsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.accounts,
          aliasName: $_aliasNameGenerator(db.users.id, db.accounts.userId));

  $$AccountsTableProcessedTableManager get accountsRefs {
    final manager = $$AccountsTableTableManager($_db, $_db.accounts)
        .filter((f) => f.userId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_accountsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$ApiKeysTable, List<ApiKey>> _apiKeysRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.apiKeys,
          aliasName: $_aliasNameGenerator(db.users.id, db.apiKeys.userId));

  $$ApiKeysTableProcessedTableManager get apiKeysRefs {
    final manager = $$ApiKeysTableTableManager($_db, $_db.apiKeys)
        .filter((f) => f.userId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_apiKeysRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$UsersTableFilterComposer extends Composer<_$AppDatabase, $UsersTable> {
  $$UsersTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get emailVerified => $composableBuilder(
      column: $table.emailVerified, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get image => $composableBuilder(
      column: $table.image, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  Expression<bool> sessionsRefs(
      Expression<bool> Function($$SessionsTableFilterComposer f) f) {
    final $$SessionsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.sessions,
        getReferencedColumn: (t) => t.userId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$SessionsTableFilterComposer(
              $db: $db,
              $table: $db.sessions,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<bool> accountsRefs(
      Expression<bool> Function($$AccountsTableFilterComposer f) f) {
    final $$AccountsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.accounts,
        getReferencedColumn: (t) => t.userId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$AccountsTableFilterComposer(
              $db: $db,
              $table: $db.accounts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<bool> apiKeysRefs(
      Expression<bool> Function($$ApiKeysTableFilterComposer f) f) {
    final $$ApiKeysTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.apiKeys,
        getReferencedColumn: (t) => t.userId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ApiKeysTableFilterComposer(
              $db: $db,
              $table: $db.apiKeys,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$UsersTableOrderingComposer
    extends Composer<_$AppDatabase, $UsersTable> {
  $$UsersTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get emailVerified => $composableBuilder(
      column: $table.emailVerified,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get image => $composableBuilder(
      column: $table.image, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$UsersTableAnnotationComposer
    extends Composer<_$AppDatabase, $UsersTable> {
  $$UsersTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get email =>
      $composableBuilder(column: $table.email, builder: (column) => column);

  GeneratedColumn<bool> get emailVerified => $composableBuilder(
      column: $table.emailVerified, builder: (column) => column);

  GeneratedColumn<String> get image =>
      $composableBuilder(column: $table.image, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  Expression<T> sessionsRefs<T extends Object>(
      Expression<T> Function($$SessionsTableAnnotationComposer a) f) {
    final $$SessionsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.sessions,
        getReferencedColumn: (t) => t.userId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$SessionsTableAnnotationComposer(
              $db: $db,
              $table: $db.sessions,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<T> accountsRefs<T extends Object>(
      Expression<T> Function($$AccountsTableAnnotationComposer a) f) {
    final $$AccountsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.accounts,
        getReferencedColumn: (t) => t.userId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$AccountsTableAnnotationComposer(
              $db: $db,
              $table: $db.accounts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<T> apiKeysRefs<T extends Object>(
      Expression<T> Function($$ApiKeysTableAnnotationComposer a) f) {
    final $$ApiKeysTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.apiKeys,
        getReferencedColumn: (t) => t.userId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ApiKeysTableAnnotationComposer(
              $db: $db,
              $table: $db.apiKeys,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$UsersTableTableManager extends RootTableManager<
    _$AppDatabase,
    $UsersTable,
    User,
    $$UsersTableFilterComposer,
    $$UsersTableOrderingComposer,
    $$UsersTableAnnotationComposer,
    $$UsersTableCreateCompanionBuilder,
    $$UsersTableUpdateCompanionBuilder,
    (User, $$UsersTableReferences),
    User,
    PrefetchHooks Function(
        {bool sessionsRefs, bool accountsRefs, bool apiKeysRefs})> {
  $$UsersTableTableManager(_$AppDatabase db, $UsersTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$UsersTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$UsersTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$UsersTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> email = const Value.absent(),
            Value<bool> emailVerified = const Value.absent(),
            Value<String?> image = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UsersCompanion(
            id: id,
            name: name,
            email: email,
            emailVerified: emailVerified,
            image: image,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String name,
            required String email,
            Value<bool> emailVerified = const Value.absent(),
            Value<String?> image = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UsersCompanion.insert(
            id: id,
            name: name,
            email: email,
            emailVerified: emailVerified,
            image: image,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$UsersTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: (
              {sessionsRefs = false,
              accountsRefs = false,
              apiKeysRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (sessionsRefs) db.sessions,
                if (accountsRefs) db.accounts,
                if (apiKeysRefs) db.apiKeys
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (sessionsRefs)
                    await $_getPrefetchedData<User, $UsersTable, Session>(
                        currentTable: table,
                        referencedTable:
                            $$UsersTableReferences._sessionsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$UsersTableReferences(db, table, p0).sessionsRefs,
                        referencedItemsForCurrentItem: (item,
                                referencedItems) =>
                            referencedItems.where((e) => e.userId == item.id),
                        typedResults: items),
                  if (accountsRefs)
                    await $_getPrefetchedData<User, $UsersTable, Account>(
                        currentTable: table,
                        referencedTable:
                            $$UsersTableReferences._accountsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$UsersTableReferences(db, table, p0).accountsRefs,
                        referencedItemsForCurrentItem: (item,
                                referencedItems) =>
                            referencedItems.where((e) => e.userId == item.id),
                        typedResults: items),
                  if (apiKeysRefs)
                    await $_getPrefetchedData<User, $UsersTable, ApiKey>(
                        currentTable: table,
                        referencedTable:
                            $$UsersTableReferences._apiKeysRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$UsersTableReferences(db, table, p0).apiKeysRefs,
                        referencedItemsForCurrentItem: (item,
                                referencedItems) =>
                            referencedItems.where((e) => e.userId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$UsersTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $UsersTable,
    User,
    $$UsersTableFilterComposer,
    $$UsersTableOrderingComposer,
    $$UsersTableAnnotationComposer,
    $$UsersTableCreateCompanionBuilder,
    $$UsersTableUpdateCompanionBuilder,
    (User, $$UsersTableReferences),
    User,
    PrefetchHooks Function(
        {bool sessionsRefs, bool accountsRefs, bool apiKeysRefs})>;
typedef $$SessionsTableCreateCompanionBuilder = SessionsCompanion Function({
  required String id,
  required DateTime expiresAt,
  required String token,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<String?> ipAddress,
  Value<String?> userAgent,
  required String userId,
  Value<int> rowid,
});
typedef $$SessionsTableUpdateCompanionBuilder = SessionsCompanion Function({
  Value<String> id,
  Value<DateTime> expiresAt,
  Value<String> token,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<String?> ipAddress,
  Value<String?> userAgent,
  Value<String> userId,
  Value<int> rowid,
});

final class $$SessionsTableReferences
    extends BaseReferences<_$AppDatabase, $SessionsTable, Session> {
  $$SessionsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $UsersTable _userIdTable(_$AppDatabase db) => db.users
      .createAlias($_aliasNameGenerator(db.sessions.userId, db.users.id));

  $$UsersTableProcessedTableManager get userId {
    final $_column = $_itemColumn<String>('user_id')!;

    final manager = $$UsersTableTableManager($_db, $_db.users)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_userIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$SessionsTableFilterComposer
    extends Composer<_$AppDatabase, $SessionsTable> {
  $$SessionsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get expiresAt => $composableBuilder(
      column: $table.expiresAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get token => $composableBuilder(
      column: $table.token, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get ipAddress => $composableBuilder(
      column: $table.ipAddress, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get userAgent => $composableBuilder(
      column: $table.userAgent, builder: (column) => ColumnFilters(column));

  $$UsersTableFilterComposer get userId {
    final $$UsersTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableFilterComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SessionsTableOrderingComposer
    extends Composer<_$AppDatabase, $SessionsTable> {
  $$SessionsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get expiresAt => $composableBuilder(
      column: $table.expiresAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get token => $composableBuilder(
      column: $table.token, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get ipAddress => $composableBuilder(
      column: $table.ipAddress, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get userAgent => $composableBuilder(
      column: $table.userAgent, builder: (column) => ColumnOrderings(column));

  $$UsersTableOrderingComposer get userId {
    final $$UsersTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableOrderingComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SessionsTableAnnotationComposer
    extends Composer<_$AppDatabase, $SessionsTable> {
  $$SessionsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<DateTime> get expiresAt =>
      $composableBuilder(column: $table.expiresAt, builder: (column) => column);

  GeneratedColumn<String> get token =>
      $composableBuilder(column: $table.token, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<String> get ipAddress =>
      $composableBuilder(column: $table.ipAddress, builder: (column) => column);

  GeneratedColumn<String> get userAgent =>
      $composableBuilder(column: $table.userAgent, builder: (column) => column);

  $$UsersTableAnnotationComposer get userId {
    final $$UsersTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableAnnotationComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SessionsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $SessionsTable,
    Session,
    $$SessionsTableFilterComposer,
    $$SessionsTableOrderingComposer,
    $$SessionsTableAnnotationComposer,
    $$SessionsTableCreateCompanionBuilder,
    $$SessionsTableUpdateCompanionBuilder,
    (Session, $$SessionsTableReferences),
    Session,
    PrefetchHooks Function({bool userId})> {
  $$SessionsTableTableManager(_$AppDatabase db, $SessionsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SessionsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SessionsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SessionsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<DateTime> expiresAt = const Value.absent(),
            Value<String> token = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<String?> ipAddress = const Value.absent(),
            Value<String?> userAgent = const Value.absent(),
            Value<String> userId = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              SessionsCompanion(
            id: id,
            expiresAt: expiresAt,
            token: token,
            createdAt: createdAt,
            updatedAt: updatedAt,
            ipAddress: ipAddress,
            userAgent: userAgent,
            userId: userId,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required DateTime expiresAt,
            required String token,
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<String?> ipAddress = const Value.absent(),
            Value<String?> userAgent = const Value.absent(),
            required String userId,
            Value<int> rowid = const Value.absent(),
          }) =>
              SessionsCompanion.insert(
            id: id,
            expiresAt: expiresAt,
            token: token,
            createdAt: createdAt,
            updatedAt: updatedAt,
            ipAddress: ipAddress,
            userAgent: userAgent,
            userId: userId,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$SessionsTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({userId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (userId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.userId,
                    referencedTable: $$SessionsTableReferences._userIdTable(db),
                    referencedColumn:
                        $$SessionsTableReferences._userIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$SessionsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $SessionsTable,
    Session,
    $$SessionsTableFilterComposer,
    $$SessionsTableOrderingComposer,
    $$SessionsTableAnnotationComposer,
    $$SessionsTableCreateCompanionBuilder,
    $$SessionsTableUpdateCompanionBuilder,
    (Session, $$SessionsTableReferences),
    Session,
    PrefetchHooks Function({bool userId})>;
typedef $$AccountsTableCreateCompanionBuilder = AccountsCompanion Function({
  required String id,
  required String accountId,
  required String providerId,
  required String userId,
  Value<String?> accessToken,
  Value<String?> refreshToken,
  Value<String?> idToken,
  Value<DateTime?> accessTokenExpiresAt,
  Value<DateTime?> refreshTokenExpiresAt,
  Value<String?> scope,
  Value<String?> password,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$AccountsTableUpdateCompanionBuilder = AccountsCompanion Function({
  Value<String> id,
  Value<String> accountId,
  Value<String> providerId,
  Value<String> userId,
  Value<String?> accessToken,
  Value<String?> refreshToken,
  Value<String?> idToken,
  Value<DateTime?> accessTokenExpiresAt,
  Value<DateTime?> refreshTokenExpiresAt,
  Value<String?> scope,
  Value<String?> password,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

final class $$AccountsTableReferences
    extends BaseReferences<_$AppDatabase, $AccountsTable, Account> {
  $$AccountsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $UsersTable _userIdTable(_$AppDatabase db) => db.users
      .createAlias($_aliasNameGenerator(db.accounts.userId, db.users.id));

  $$UsersTableProcessedTableManager get userId {
    final $_column = $_itemColumn<String>('user_id')!;

    final manager = $$UsersTableTableManager($_db, $_db.users)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_userIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$AccountsTableFilterComposer
    extends Composer<_$AppDatabase, $AccountsTable> {
  $$AccountsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get accountId => $composableBuilder(
      column: $table.accountId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get providerId => $composableBuilder(
      column: $table.providerId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get accessToken => $composableBuilder(
      column: $table.accessToken, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get refreshToken => $composableBuilder(
      column: $table.refreshToken, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get idToken => $composableBuilder(
      column: $table.idToken, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get accessTokenExpiresAt => $composableBuilder(
      column: $table.accessTokenExpiresAt,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get refreshTokenExpiresAt => $composableBuilder(
      column: $table.refreshTokenExpiresAt,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get scope => $composableBuilder(
      column: $table.scope, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get password => $composableBuilder(
      column: $table.password, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  $$UsersTableFilterComposer get userId {
    final $$UsersTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableFilterComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$AccountsTableOrderingComposer
    extends Composer<_$AppDatabase, $AccountsTable> {
  $$AccountsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get accountId => $composableBuilder(
      column: $table.accountId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get providerId => $composableBuilder(
      column: $table.providerId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get accessToken => $composableBuilder(
      column: $table.accessToken, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get refreshToken => $composableBuilder(
      column: $table.refreshToken,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get idToken => $composableBuilder(
      column: $table.idToken, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get accessTokenExpiresAt => $composableBuilder(
      column: $table.accessTokenExpiresAt,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get refreshTokenExpiresAt => $composableBuilder(
      column: $table.refreshTokenExpiresAt,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get scope => $composableBuilder(
      column: $table.scope, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get password => $composableBuilder(
      column: $table.password, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  $$UsersTableOrderingComposer get userId {
    final $$UsersTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableOrderingComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$AccountsTableAnnotationComposer
    extends Composer<_$AppDatabase, $AccountsTable> {
  $$AccountsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get accountId =>
      $composableBuilder(column: $table.accountId, builder: (column) => column);

  GeneratedColumn<String> get providerId => $composableBuilder(
      column: $table.providerId, builder: (column) => column);

  GeneratedColumn<String> get accessToken => $composableBuilder(
      column: $table.accessToken, builder: (column) => column);

  GeneratedColumn<String> get refreshToken => $composableBuilder(
      column: $table.refreshToken, builder: (column) => column);

  GeneratedColumn<String> get idToken =>
      $composableBuilder(column: $table.idToken, builder: (column) => column);

  GeneratedColumn<DateTime> get accessTokenExpiresAt => $composableBuilder(
      column: $table.accessTokenExpiresAt, builder: (column) => column);

  GeneratedColumn<DateTime> get refreshTokenExpiresAt => $composableBuilder(
      column: $table.refreshTokenExpiresAt, builder: (column) => column);

  GeneratedColumn<String> get scope =>
      $composableBuilder(column: $table.scope, builder: (column) => column);

  GeneratedColumn<String> get password =>
      $composableBuilder(column: $table.password, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  $$UsersTableAnnotationComposer get userId {
    final $$UsersTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableAnnotationComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$AccountsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AccountsTable,
    Account,
    $$AccountsTableFilterComposer,
    $$AccountsTableOrderingComposer,
    $$AccountsTableAnnotationComposer,
    $$AccountsTableCreateCompanionBuilder,
    $$AccountsTableUpdateCompanionBuilder,
    (Account, $$AccountsTableReferences),
    Account,
    PrefetchHooks Function({bool userId})> {
  $$AccountsTableTableManager(_$AppDatabase db, $AccountsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AccountsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AccountsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AccountsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> accountId = const Value.absent(),
            Value<String> providerId = const Value.absent(),
            Value<String> userId = const Value.absent(),
            Value<String?> accessToken = const Value.absent(),
            Value<String?> refreshToken = const Value.absent(),
            Value<String?> idToken = const Value.absent(),
            Value<DateTime?> accessTokenExpiresAt = const Value.absent(),
            Value<DateTime?> refreshTokenExpiresAt = const Value.absent(),
            Value<String?> scope = const Value.absent(),
            Value<String?> password = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AccountsCompanion(
            id: id,
            accountId: accountId,
            providerId: providerId,
            userId: userId,
            accessToken: accessToken,
            refreshToken: refreshToken,
            idToken: idToken,
            accessTokenExpiresAt: accessTokenExpiresAt,
            refreshTokenExpiresAt: refreshTokenExpiresAt,
            scope: scope,
            password: password,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String accountId,
            required String providerId,
            required String userId,
            Value<String?> accessToken = const Value.absent(),
            Value<String?> refreshToken = const Value.absent(),
            Value<String?> idToken = const Value.absent(),
            Value<DateTime?> accessTokenExpiresAt = const Value.absent(),
            Value<DateTime?> refreshTokenExpiresAt = const Value.absent(),
            Value<String?> scope = const Value.absent(),
            Value<String?> password = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AccountsCompanion.insert(
            id: id,
            accountId: accountId,
            providerId: providerId,
            userId: userId,
            accessToken: accessToken,
            refreshToken: refreshToken,
            idToken: idToken,
            accessTokenExpiresAt: accessTokenExpiresAt,
            refreshTokenExpiresAt: refreshTokenExpiresAt,
            scope: scope,
            password: password,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$AccountsTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({userId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (userId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.userId,
                    referencedTable: $$AccountsTableReferences._userIdTable(db),
                    referencedColumn:
                        $$AccountsTableReferences._userIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$AccountsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AccountsTable,
    Account,
    $$AccountsTableFilterComposer,
    $$AccountsTableOrderingComposer,
    $$AccountsTableAnnotationComposer,
    $$AccountsTableCreateCompanionBuilder,
    $$AccountsTableUpdateCompanionBuilder,
    (Account, $$AccountsTableReferences),
    Account,
    PrefetchHooks Function({bool userId})>;
typedef $$VerificationsTableCreateCompanionBuilder = VerificationsCompanion
    Function({
  required String id,
  required String identifier,
  required String value,
  required DateTime expiresAt,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$VerificationsTableUpdateCompanionBuilder = VerificationsCompanion
    Function({
  Value<String> id,
  Value<String> identifier,
  Value<String> value,
  Value<DateTime> expiresAt,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$VerificationsTableFilterComposer
    extends Composer<_$AppDatabase, $VerificationsTable> {
  $$VerificationsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get identifier => $composableBuilder(
      column: $table.identifier, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get expiresAt => $composableBuilder(
      column: $table.expiresAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$VerificationsTableOrderingComposer
    extends Composer<_$AppDatabase, $VerificationsTable> {
  $$VerificationsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get identifier => $composableBuilder(
      column: $table.identifier, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get expiresAt => $composableBuilder(
      column: $table.expiresAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$VerificationsTableAnnotationComposer
    extends Composer<_$AppDatabase, $VerificationsTable> {
  $$VerificationsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get identifier => $composableBuilder(
      column: $table.identifier, builder: (column) => column);

  GeneratedColumn<String> get value =>
      $composableBuilder(column: $table.value, builder: (column) => column);

  GeneratedColumn<DateTime> get expiresAt =>
      $composableBuilder(column: $table.expiresAt, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$VerificationsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $VerificationsTable,
    Verification,
    $$VerificationsTableFilterComposer,
    $$VerificationsTableOrderingComposer,
    $$VerificationsTableAnnotationComposer,
    $$VerificationsTableCreateCompanionBuilder,
    $$VerificationsTableUpdateCompanionBuilder,
    (
      Verification,
      BaseReferences<_$AppDatabase, $VerificationsTable, Verification>
    ),
    Verification,
    PrefetchHooks Function()> {
  $$VerificationsTableTableManager(_$AppDatabase db, $VerificationsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$VerificationsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$VerificationsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$VerificationsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> identifier = const Value.absent(),
            Value<String> value = const Value.absent(),
            Value<DateTime> expiresAt = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              VerificationsCompanion(
            id: id,
            identifier: identifier,
            value: value,
            expiresAt: expiresAt,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String identifier,
            required String value,
            required DateTime expiresAt,
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              VerificationsCompanion.insert(
            id: id,
            identifier: identifier,
            value: value,
            expiresAt: expiresAt,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$VerificationsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $VerificationsTable,
    Verification,
    $$VerificationsTableFilterComposer,
    $$VerificationsTableOrderingComposer,
    $$VerificationsTableAnnotationComposer,
    $$VerificationsTableCreateCompanionBuilder,
    $$VerificationsTableUpdateCompanionBuilder,
    (
      Verification,
      BaseReferences<_$AppDatabase, $VerificationsTable, Verification>
    ),
    Verification,
    PrefetchHooks Function()>;
typedef $$NamespacesTableCreateCompanionBuilder = NamespacesCompanion Function({
  required String uuid,
  required String name,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$NamespacesTableUpdateCompanionBuilder = NamespacesCompanion Function({
  Value<String> uuid,
  Value<String> name,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

final class $$NamespacesTableReferences
    extends BaseReferences<_$AppDatabase, $NamespacesTable, Namespace> {
  $$NamespacesTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$EndpointsTable, List<Endpoint>>
      _endpointsRefsTable(_$AppDatabase db) =>
          MultiTypedResultKey.fromTable(db.endpoints,
              aliasName: $_aliasNameGenerator(
                  db.namespaces.uuid, db.endpoints.namespaceUuid));

  $$EndpointsTableProcessedTableManager get endpointsRefs {
    final manager = $$EndpointsTableTableManager($_db, $_db.endpoints).filter(
        (f) => f.namespaceUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache = $_typedResult.readTableOrNull(_endpointsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$NamespaceServerMappingsTable,
      List<NamespaceServerMapping>> _namespaceServerMappingsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.namespaceServerMappings,
          aliasName: $_aliasNameGenerator(
              db.namespaces.uuid, db.namespaceServerMappings.namespaceUuid));

  $$NamespaceServerMappingsTableProcessedTableManager
      get namespaceServerMappingsRefs {
    final manager = $$NamespaceServerMappingsTableTableManager(
            $_db, $_db.namespaceServerMappings)
        .filter((f) =>
            f.namespaceUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache =
        $_typedResult.readTableOrNull(_namespaceServerMappingsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }

  static MultiTypedResultKey<$NamespaceToolMappingsTable,
      List<NamespaceToolMapping>> _namespaceToolMappingsRefsTable(
          _$AppDatabase db) =>
      MultiTypedResultKey.fromTable(db.namespaceToolMappings,
          aliasName: $_aliasNameGenerator(
              db.namespaces.uuid, db.namespaceToolMappings.namespaceUuid));

  $$NamespaceToolMappingsTableProcessedTableManager
      get namespaceToolMappingsRefs {
    final manager = $$NamespaceToolMappingsTableTableManager(
            $_db, $_db.namespaceToolMappings)
        .filter((f) =>
            f.namespaceUuid.uuid.sqlEquals($_itemColumn<String>('uuid')!));

    final cache =
        $_typedResult.readTableOrNull(_namespaceToolMappingsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$NamespacesTableFilterComposer
    extends Composer<_$AppDatabase, $NamespacesTable> {
  $$NamespacesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  Expression<bool> endpointsRefs(
      Expression<bool> Function($$EndpointsTableFilterComposer f) f) {
    final $$EndpointsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.uuid,
        referencedTable: $db.endpoints,
        getReferencedColumn: (t) => t.namespaceUuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$EndpointsTableFilterComposer(
              $db: $db,
              $table: $db.endpoints,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<bool> namespaceServerMappingsRefs(
      Expression<bool> Function($$NamespaceServerMappingsTableFilterComposer f)
          f) {
    final $$NamespaceServerMappingsTableFilterComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceServerMappings,
            getReferencedColumn: (t) => t.namespaceUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceServerMappingsTableFilterComposer(
                  $db: $db,
                  $table: $db.namespaceServerMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }

  Expression<bool> namespaceToolMappingsRefs(
      Expression<bool> Function($$NamespaceToolMappingsTableFilterComposer f)
          f) {
    final $$NamespaceToolMappingsTableFilterComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceToolMappings,
            getReferencedColumn: (t) => t.namespaceUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceToolMappingsTableFilterComposer(
                  $db: $db,
                  $table: $db.namespaceToolMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$NamespacesTableOrderingComposer
    extends Composer<_$AppDatabase, $NamespacesTable> {
  $$NamespacesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$NamespacesTableAnnotationComposer
    extends Composer<_$AppDatabase, $NamespacesTable> {
  $$NamespacesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  Expression<T> endpointsRefs<T extends Object>(
      Expression<T> Function($$EndpointsTableAnnotationComposer a) f) {
    final $$EndpointsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.uuid,
        referencedTable: $db.endpoints,
        getReferencedColumn: (t) => t.namespaceUuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$EndpointsTableAnnotationComposer(
              $db: $db,
              $table: $db.endpoints,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  Expression<T> namespaceServerMappingsRefs<T extends Object>(
      Expression<T> Function($$NamespaceServerMappingsTableAnnotationComposer a)
          f) {
    final $$NamespaceServerMappingsTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceServerMappings,
            getReferencedColumn: (t) => t.namespaceUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceServerMappingsTableAnnotationComposer(
                  $db: $db,
                  $table: $db.namespaceServerMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }

  Expression<T> namespaceToolMappingsRefs<T extends Object>(
      Expression<T> Function($$NamespaceToolMappingsTableAnnotationComposer a)
          f) {
    final $$NamespaceToolMappingsTableAnnotationComposer composer =
        $composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.uuid,
            referencedTable: $db.namespaceToolMappings,
            getReferencedColumn: (t) => t.namespaceUuid,
            builder: (joinBuilder,
                    {$addJoinBuilderToRootComposer,
                    $removeJoinBuilderFromRootComposer}) =>
                $$NamespaceToolMappingsTableAnnotationComposer(
                  $db: $db,
                  $table: $db.namespaceToolMappings,
                  $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                  joinBuilder: joinBuilder,
                  $removeJoinBuilderFromRootComposer:
                      $removeJoinBuilderFromRootComposer,
                ));
    return f(composer);
  }
}

class $$NamespacesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $NamespacesTable,
    Namespace,
    $$NamespacesTableFilterComposer,
    $$NamespacesTableOrderingComposer,
    $$NamespacesTableAnnotationComposer,
    $$NamespacesTableCreateCompanionBuilder,
    $$NamespacesTableUpdateCompanionBuilder,
    (Namespace, $$NamespacesTableReferences),
    Namespace,
    PrefetchHooks Function(
        {bool endpointsRefs,
        bool namespaceServerMappingsRefs,
        bool namespaceToolMappingsRefs})> {
  $$NamespacesTableTableManager(_$AppDatabase db, $NamespacesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$NamespacesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$NamespacesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$NamespacesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              NamespacesCompanion(
            uuid: uuid,
            name: name,
            description: description,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String name,
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              NamespacesCompanion.insert(
            uuid: uuid,
            name: name,
            description: description,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$NamespacesTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: (
              {endpointsRefs = false,
              namespaceServerMappingsRefs = false,
              namespaceToolMappingsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (endpointsRefs) db.endpoints,
                if (namespaceServerMappingsRefs) db.namespaceServerMappings,
                if (namespaceToolMappingsRefs) db.namespaceToolMappings
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (endpointsRefs)
                    await $_getPrefetchedData<Namespace, $NamespacesTable,
                            Endpoint>(
                        currentTable: table,
                        referencedTable:
                            $$NamespacesTableReferences._endpointsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$NamespacesTableReferences(db, table, p0)
                                .endpointsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.namespaceUuid == item.uuid),
                        typedResults: items),
                  if (namespaceServerMappingsRefs)
                    await $_getPrefetchedData<Namespace, $NamespacesTable,
                            NamespaceServerMapping>(
                        currentTable: table,
                        referencedTable: $$NamespacesTableReferences
                            ._namespaceServerMappingsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$NamespacesTableReferences(db, table, p0)
                                .namespaceServerMappingsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.namespaceUuid == item.uuid),
                        typedResults: items),
                  if (namespaceToolMappingsRefs)
                    await $_getPrefetchedData<Namespace, $NamespacesTable, NamespaceToolMapping>(
                        currentTable: table,
                        referencedTable: $$NamespacesTableReferences
                            ._namespaceToolMappingsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$NamespacesTableReferences(db, table, p0)
                                .namespaceToolMappingsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.namespaceUuid == item.uuid),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$NamespacesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $NamespacesTable,
    Namespace,
    $$NamespacesTableFilterComposer,
    $$NamespacesTableOrderingComposer,
    $$NamespacesTableAnnotationComposer,
    $$NamespacesTableCreateCompanionBuilder,
    $$NamespacesTableUpdateCompanionBuilder,
    (Namespace, $$NamespacesTableReferences),
    Namespace,
    PrefetchHooks Function(
        {bool endpointsRefs,
        bool namespaceServerMappingsRefs,
        bool namespaceToolMappingsRefs})>;
typedef $$EndpointsTableCreateCompanionBuilder = EndpointsCompanion Function({
  required String uuid,
  required String name,
  Value<String?> description,
  required String namespaceUuid,
  Value<bool> enableApiKeyAuth,
  Value<bool> useQueryParamAuth,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$EndpointsTableUpdateCompanionBuilder = EndpointsCompanion Function({
  Value<String> uuid,
  Value<String> name,
  Value<String?> description,
  Value<String> namespaceUuid,
  Value<bool> enableApiKeyAuth,
  Value<bool> useQueryParamAuth,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

final class $$EndpointsTableReferences
    extends BaseReferences<_$AppDatabase, $EndpointsTable, Endpoint> {
  $$EndpointsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $NamespacesTable _namespaceUuidTable(_$AppDatabase db) =>
      db.namespaces.createAlias(
          $_aliasNameGenerator(db.endpoints.namespaceUuid, db.namespaces.uuid));

  $$NamespacesTableProcessedTableManager get namespaceUuid {
    final $_column = $_itemColumn<String>('namespace_uuid')!;

    final manager = $$NamespacesTableTableManager($_db, $_db.namespaces)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_namespaceUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$EndpointsTableFilterComposer
    extends Composer<_$AppDatabase, $EndpointsTable> {
  $$EndpointsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get enableApiKeyAuth => $composableBuilder(
      column: $table.enableApiKeyAuth,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get useQueryParamAuth => $composableBuilder(
      column: $table.useQueryParamAuth,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  $$NamespacesTableFilterComposer get namespaceUuid {
    final $$NamespacesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableFilterComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$EndpointsTableOrderingComposer
    extends Composer<_$AppDatabase, $EndpointsTable> {
  $$EndpointsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get enableApiKeyAuth => $composableBuilder(
      column: $table.enableApiKeyAuth,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get useQueryParamAuth => $composableBuilder(
      column: $table.useQueryParamAuth,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  $$NamespacesTableOrderingComposer get namespaceUuid {
    final $$NamespacesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableOrderingComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$EndpointsTableAnnotationComposer
    extends Composer<_$AppDatabase, $EndpointsTable> {
  $$EndpointsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<bool> get enableApiKeyAuth => $composableBuilder(
      column: $table.enableApiKeyAuth, builder: (column) => column);

  GeneratedColumn<bool> get useQueryParamAuth => $composableBuilder(
      column: $table.useQueryParamAuth, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  $$NamespacesTableAnnotationComposer get namespaceUuid {
    final $$NamespacesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableAnnotationComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$EndpointsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $EndpointsTable,
    Endpoint,
    $$EndpointsTableFilterComposer,
    $$EndpointsTableOrderingComposer,
    $$EndpointsTableAnnotationComposer,
    $$EndpointsTableCreateCompanionBuilder,
    $$EndpointsTableUpdateCompanionBuilder,
    (Endpoint, $$EndpointsTableReferences),
    Endpoint,
    PrefetchHooks Function({bool namespaceUuid})> {
  $$EndpointsTableTableManager(_$AppDatabase db, $EndpointsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$EndpointsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$EndpointsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$EndpointsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<String> namespaceUuid = const Value.absent(),
            Value<bool> enableApiKeyAuth = const Value.absent(),
            Value<bool> useQueryParamAuth = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              EndpointsCompanion(
            uuid: uuid,
            name: name,
            description: description,
            namespaceUuid: namespaceUuid,
            enableApiKeyAuth: enableApiKeyAuth,
            useQueryParamAuth: useQueryParamAuth,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String name,
            Value<String?> description = const Value.absent(),
            required String namespaceUuid,
            Value<bool> enableApiKeyAuth = const Value.absent(),
            Value<bool> useQueryParamAuth = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              EndpointsCompanion.insert(
            uuid: uuid,
            name: name,
            description: description,
            namespaceUuid: namespaceUuid,
            enableApiKeyAuth: enableApiKeyAuth,
            useQueryParamAuth: useQueryParamAuth,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$EndpointsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({namespaceUuid = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (namespaceUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.namespaceUuid,
                    referencedTable:
                        $$EndpointsTableReferences._namespaceUuidTable(db),
                    referencedColumn:
                        $$EndpointsTableReferences._namespaceUuidTable(db).uuid,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$EndpointsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $EndpointsTable,
    Endpoint,
    $$EndpointsTableFilterComposer,
    $$EndpointsTableOrderingComposer,
    $$EndpointsTableAnnotationComposer,
    $$EndpointsTableCreateCompanionBuilder,
    $$EndpointsTableUpdateCompanionBuilder,
    (Endpoint, $$EndpointsTableReferences),
    Endpoint,
    PrefetchHooks Function({bool namespaceUuid})>;
typedef $$NamespaceServerMappingsTableCreateCompanionBuilder
    = NamespaceServerMappingsCompanion Function({
  required String uuid,
  required String namespaceUuid,
  required String mcpServerUuid,
  Value<McpServerStatus> status,
  Value<DateTime> createdAt,
  Value<int> rowid,
});
typedef $$NamespaceServerMappingsTableUpdateCompanionBuilder
    = NamespaceServerMappingsCompanion Function({
  Value<String> uuid,
  Value<String> namespaceUuid,
  Value<String> mcpServerUuid,
  Value<McpServerStatus> status,
  Value<DateTime> createdAt,
  Value<int> rowid,
});

final class $$NamespaceServerMappingsTableReferences extends BaseReferences<
    _$AppDatabase, $NamespaceServerMappingsTable, NamespaceServerMapping> {
  $$NamespaceServerMappingsTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static $NamespacesTable _namespaceUuidTable(_$AppDatabase db) =>
      db.namespaces.createAlias($_aliasNameGenerator(
          db.namespaceServerMappings.namespaceUuid, db.namespaces.uuid));

  $$NamespacesTableProcessedTableManager get namespaceUuid {
    final $_column = $_itemColumn<String>('namespace_uuid')!;

    final manager = $$NamespacesTableTableManager($_db, $_db.namespaces)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_namespaceUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static $McpServersTable _mcpServerUuidTable(_$AppDatabase db) =>
      db.mcpServers.createAlias($_aliasNameGenerator(
          db.namespaceServerMappings.mcpServerUuid, db.mcpServers.uuid));

  $$McpServersTableProcessedTableManager get mcpServerUuid {
    final $_column = $_itemColumn<String>('mcp_server_uuid')!;

    final manager = $$McpServersTableTableManager($_db, $_db.mcpServers)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_mcpServerUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$NamespaceServerMappingsTableFilterComposer
    extends Composer<_$AppDatabase, $NamespaceServerMappingsTable> {
  $$NamespaceServerMappingsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<McpServerStatus, McpServerStatus, int>
      get status => $composableBuilder(
          column: $table.status,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  $$NamespacesTableFilterComposer get namespaceUuid {
    final $$NamespacesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableFilterComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$McpServersTableFilterComposer get mcpServerUuid {
    final $$McpServersTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableFilterComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$NamespaceServerMappingsTableOrderingComposer
    extends Composer<_$AppDatabase, $NamespaceServerMappingsTable> {
  $$NamespaceServerMappingsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  $$NamespacesTableOrderingComposer get namespaceUuid {
    final $$NamespacesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableOrderingComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$McpServersTableOrderingComposer get mcpServerUuid {
    final $$McpServersTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableOrderingComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$NamespaceServerMappingsTableAnnotationComposer
    extends Composer<_$AppDatabase, $NamespaceServerMappingsTable> {
  $$NamespaceServerMappingsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumnWithTypeConverter<McpServerStatus, int> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  $$NamespacesTableAnnotationComposer get namespaceUuid {
    final $$NamespacesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableAnnotationComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$McpServersTableAnnotationComposer get mcpServerUuid {
    final $$McpServersTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableAnnotationComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$NamespaceServerMappingsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $NamespaceServerMappingsTable,
    NamespaceServerMapping,
    $$NamespaceServerMappingsTableFilterComposer,
    $$NamespaceServerMappingsTableOrderingComposer,
    $$NamespaceServerMappingsTableAnnotationComposer,
    $$NamespaceServerMappingsTableCreateCompanionBuilder,
    $$NamespaceServerMappingsTableUpdateCompanionBuilder,
    (NamespaceServerMapping, $$NamespaceServerMappingsTableReferences),
    NamespaceServerMapping,
    PrefetchHooks Function({bool namespaceUuid, bool mcpServerUuid})> {
  $$NamespaceServerMappingsTableTableManager(
      _$AppDatabase db, $NamespaceServerMappingsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$NamespaceServerMappingsTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$NamespaceServerMappingsTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$NamespaceServerMappingsTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> namespaceUuid = const Value.absent(),
            Value<String> mcpServerUuid = const Value.absent(),
            Value<McpServerStatus> status = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              NamespaceServerMappingsCompanion(
            uuid: uuid,
            namespaceUuid: namespaceUuid,
            mcpServerUuid: mcpServerUuid,
            status: status,
            createdAt: createdAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String namespaceUuid,
            required String mcpServerUuid,
            Value<McpServerStatus> status = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              NamespaceServerMappingsCompanion.insert(
            uuid: uuid,
            namespaceUuid: namespaceUuid,
            mcpServerUuid: mcpServerUuid,
            status: status,
            createdAt: createdAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$NamespaceServerMappingsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: (
              {namespaceUuid = false, mcpServerUuid = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (namespaceUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.namespaceUuid,
                    referencedTable: $$NamespaceServerMappingsTableReferences
                        ._namespaceUuidTable(db),
                    referencedColumn: $$NamespaceServerMappingsTableReferences
                        ._namespaceUuidTable(db)
                        .uuid,
                  ) as T;
                }
                if (mcpServerUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.mcpServerUuid,
                    referencedTable: $$NamespaceServerMappingsTableReferences
                        ._mcpServerUuidTable(db),
                    referencedColumn: $$NamespaceServerMappingsTableReferences
                        ._mcpServerUuidTable(db)
                        .uuid,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$NamespaceServerMappingsTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $NamespaceServerMappingsTable,
        NamespaceServerMapping,
        $$NamespaceServerMappingsTableFilterComposer,
        $$NamespaceServerMappingsTableOrderingComposer,
        $$NamespaceServerMappingsTableAnnotationComposer,
        $$NamespaceServerMappingsTableCreateCompanionBuilder,
        $$NamespaceServerMappingsTableUpdateCompanionBuilder,
        (NamespaceServerMapping, $$NamespaceServerMappingsTableReferences),
        NamespaceServerMapping,
        PrefetchHooks Function({bool namespaceUuid, bool mcpServerUuid})>;
typedef $$NamespaceToolMappingsTableCreateCompanionBuilder
    = NamespaceToolMappingsCompanion Function({
  required String uuid,
  required String namespaceUuid,
  required String toolUuid,
  required String mcpServerUuid,
  Value<McpServerStatus> status,
  Value<DateTime> createdAt,
  Value<int> rowid,
});
typedef $$NamespaceToolMappingsTableUpdateCompanionBuilder
    = NamespaceToolMappingsCompanion Function({
  Value<String> uuid,
  Value<String> namespaceUuid,
  Value<String> toolUuid,
  Value<String> mcpServerUuid,
  Value<McpServerStatus> status,
  Value<DateTime> createdAt,
  Value<int> rowid,
});

final class $$NamespaceToolMappingsTableReferences extends BaseReferences<
    _$AppDatabase, $NamespaceToolMappingsTable, NamespaceToolMapping> {
  $$NamespaceToolMappingsTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static $NamespacesTable _namespaceUuidTable(_$AppDatabase db) =>
      db.namespaces.createAlias($_aliasNameGenerator(
          db.namespaceToolMappings.namespaceUuid, db.namespaces.uuid));

  $$NamespacesTableProcessedTableManager get namespaceUuid {
    final $_column = $_itemColumn<String>('namespace_uuid')!;

    final manager = $$NamespacesTableTableManager($_db, $_db.namespaces)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_namespaceUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static $ToolsTable _toolUuidTable(_$AppDatabase db) => db.tools.createAlias(
      $_aliasNameGenerator(db.namespaceToolMappings.toolUuid, db.tools.uuid));

  $$ToolsTableProcessedTableManager get toolUuid {
    final $_column = $_itemColumn<String>('tool_uuid')!;

    final manager = $$ToolsTableTableManager($_db, $_db.tools)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_toolUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static $McpServersTable _mcpServerUuidTable(_$AppDatabase db) =>
      db.mcpServers.createAlias($_aliasNameGenerator(
          db.namespaceToolMappings.mcpServerUuid, db.mcpServers.uuid));

  $$McpServersTableProcessedTableManager get mcpServerUuid {
    final $_column = $_itemColumn<String>('mcp_server_uuid')!;

    final manager = $$McpServersTableTableManager($_db, $_db.mcpServers)
        .filter((f) => f.uuid.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_mcpServerUuidTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$NamespaceToolMappingsTableFilterComposer
    extends Composer<_$AppDatabase, $NamespaceToolMappingsTable> {
  $$NamespaceToolMappingsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<McpServerStatus, McpServerStatus, int>
      get status => $composableBuilder(
          column: $table.status,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  $$NamespacesTableFilterComposer get namespaceUuid {
    final $$NamespacesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableFilterComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$ToolsTableFilterComposer get toolUuid {
    final $$ToolsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.toolUuid,
        referencedTable: $db.tools,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ToolsTableFilterComposer(
              $db: $db,
              $table: $db.tools,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$McpServersTableFilterComposer get mcpServerUuid {
    final $$McpServersTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableFilterComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$NamespaceToolMappingsTableOrderingComposer
    extends Composer<_$AppDatabase, $NamespaceToolMappingsTable> {
  $$NamespaceToolMappingsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  $$NamespacesTableOrderingComposer get namespaceUuid {
    final $$NamespacesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableOrderingComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$ToolsTableOrderingComposer get toolUuid {
    final $$ToolsTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.toolUuid,
        referencedTable: $db.tools,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ToolsTableOrderingComposer(
              $db: $db,
              $table: $db.tools,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$McpServersTableOrderingComposer get mcpServerUuid {
    final $$McpServersTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableOrderingComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$NamespaceToolMappingsTableAnnotationComposer
    extends Composer<_$AppDatabase, $NamespaceToolMappingsTable> {
  $$NamespaceToolMappingsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumnWithTypeConverter<McpServerStatus, int> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  $$NamespacesTableAnnotationComposer get namespaceUuid {
    final $$NamespacesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.namespaceUuid,
        referencedTable: $db.namespaces,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$NamespacesTableAnnotationComposer(
              $db: $db,
              $table: $db.namespaces,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$ToolsTableAnnotationComposer get toolUuid {
    final $$ToolsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.toolUuid,
        referencedTable: $db.tools,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ToolsTableAnnotationComposer(
              $db: $db,
              $table: $db.tools,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$McpServersTableAnnotationComposer get mcpServerUuid {
    final $$McpServersTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.mcpServerUuid,
        referencedTable: $db.mcpServers,
        getReferencedColumn: (t) => t.uuid,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$McpServersTableAnnotationComposer(
              $db: $db,
              $table: $db.mcpServers,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$NamespaceToolMappingsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $NamespaceToolMappingsTable,
    NamespaceToolMapping,
    $$NamespaceToolMappingsTableFilterComposer,
    $$NamespaceToolMappingsTableOrderingComposer,
    $$NamespaceToolMappingsTableAnnotationComposer,
    $$NamespaceToolMappingsTableCreateCompanionBuilder,
    $$NamespaceToolMappingsTableUpdateCompanionBuilder,
    (NamespaceToolMapping, $$NamespaceToolMappingsTableReferences),
    NamespaceToolMapping,
    PrefetchHooks Function(
        {bool namespaceUuid, bool toolUuid, bool mcpServerUuid})> {
  $$NamespaceToolMappingsTableTableManager(
      _$AppDatabase db, $NamespaceToolMappingsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$NamespaceToolMappingsTableFilterComposer(
                  $db: db, $table: table),
          createOrderingComposer: () =>
              $$NamespaceToolMappingsTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$NamespaceToolMappingsTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> namespaceUuid = const Value.absent(),
            Value<String> toolUuid = const Value.absent(),
            Value<String> mcpServerUuid = const Value.absent(),
            Value<McpServerStatus> status = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              NamespaceToolMappingsCompanion(
            uuid: uuid,
            namespaceUuid: namespaceUuid,
            toolUuid: toolUuid,
            mcpServerUuid: mcpServerUuid,
            status: status,
            createdAt: createdAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String namespaceUuid,
            required String toolUuid,
            required String mcpServerUuid,
            Value<McpServerStatus> status = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              NamespaceToolMappingsCompanion.insert(
            uuid: uuid,
            namespaceUuid: namespaceUuid,
            toolUuid: toolUuid,
            mcpServerUuid: mcpServerUuid,
            status: status,
            createdAt: createdAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$NamespaceToolMappingsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: (
              {namespaceUuid = false,
              toolUuid = false,
              mcpServerUuid = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (namespaceUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.namespaceUuid,
                    referencedTable: $$NamespaceToolMappingsTableReferences
                        ._namespaceUuidTable(db),
                    referencedColumn: $$NamespaceToolMappingsTableReferences
                        ._namespaceUuidTable(db)
                        .uuid,
                  ) as T;
                }
                if (toolUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.toolUuid,
                    referencedTable: $$NamespaceToolMappingsTableReferences
                        ._toolUuidTable(db),
                    referencedColumn: $$NamespaceToolMappingsTableReferences
                        ._toolUuidTable(db)
                        .uuid,
                  ) as T;
                }
                if (mcpServerUuid) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.mcpServerUuid,
                    referencedTable: $$NamespaceToolMappingsTableReferences
                        ._mcpServerUuidTable(db),
                    referencedColumn: $$NamespaceToolMappingsTableReferences
                        ._mcpServerUuidTable(db)
                        .uuid,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$NamespaceToolMappingsTableProcessedTableManager
    = ProcessedTableManager<
        _$AppDatabase,
        $NamespaceToolMappingsTable,
        NamespaceToolMapping,
        $$NamespaceToolMappingsTableFilterComposer,
        $$NamespaceToolMappingsTableOrderingComposer,
        $$NamespaceToolMappingsTableAnnotationComposer,
        $$NamespaceToolMappingsTableCreateCompanionBuilder,
        $$NamespaceToolMappingsTableUpdateCompanionBuilder,
        (NamespaceToolMapping, $$NamespaceToolMappingsTableReferences),
        NamespaceToolMapping,
        PrefetchHooks Function(
            {bool namespaceUuid, bool toolUuid, bool mcpServerUuid})>;
typedef $$ApiKeysTableCreateCompanionBuilder = ApiKeysCompanion Function({
  required String uuid,
  required String name,
  required String key,
  required String userId,
  Value<DateTime> createdAt,
  Value<bool> isActive,
  Value<int> rowid,
});
typedef $$ApiKeysTableUpdateCompanionBuilder = ApiKeysCompanion Function({
  Value<String> uuid,
  Value<String> name,
  Value<String> key,
  Value<String> userId,
  Value<DateTime> createdAt,
  Value<bool> isActive,
  Value<int> rowid,
});

final class $$ApiKeysTableReferences
    extends BaseReferences<_$AppDatabase, $ApiKeysTable, ApiKey> {
  $$ApiKeysTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $UsersTable _userIdTable(_$AppDatabase db) => db.users
      .createAlias($_aliasNameGenerator(db.apiKeys.userId, db.users.id));

  $$UsersTableProcessedTableManager get userId {
    final $_column = $_itemColumn<String>('user_id')!;

    final manager = $$UsersTableTableManager($_db, $_db.users)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_userIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$ApiKeysTableFilterComposer
    extends Composer<_$AppDatabase, $ApiKeysTable> {
  $$ApiKeysTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get key => $composableBuilder(
      column: $table.key, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnFilters(column));

  $$UsersTableFilterComposer get userId {
    final $$UsersTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableFilterComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$ApiKeysTableOrderingComposer
    extends Composer<_$AppDatabase, $ApiKeysTable> {
  $$ApiKeysTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get uuid => $composableBuilder(
      column: $table.uuid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get key => $composableBuilder(
      column: $table.key, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnOrderings(column));

  $$UsersTableOrderingComposer get userId {
    final $$UsersTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableOrderingComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$ApiKeysTableAnnotationComposer
    extends Composer<_$AppDatabase, $ApiKeysTable> {
  $$ApiKeysTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get uuid =>
      $composableBuilder(column: $table.uuid, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get key =>
      $composableBuilder(column: $table.key, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<bool> get isActive =>
      $composableBuilder(column: $table.isActive, builder: (column) => column);

  $$UsersTableAnnotationComposer get userId {
    final $$UsersTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.userId,
        referencedTable: $db.users,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$UsersTableAnnotationComposer(
              $db: $db,
              $table: $db.users,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$ApiKeysTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ApiKeysTable,
    ApiKey,
    $$ApiKeysTableFilterComposer,
    $$ApiKeysTableOrderingComposer,
    $$ApiKeysTableAnnotationComposer,
    $$ApiKeysTableCreateCompanionBuilder,
    $$ApiKeysTableUpdateCompanionBuilder,
    (ApiKey, $$ApiKeysTableReferences),
    ApiKey,
    PrefetchHooks Function({bool userId})> {
  $$ApiKeysTableTableManager(_$AppDatabase db, $ApiKeysTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ApiKeysTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ApiKeysTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ApiKeysTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> uuid = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> key = const Value.absent(),
            Value<String> userId = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ApiKeysCompanion(
            uuid: uuid,
            name: name,
            key: key,
            userId: userId,
            createdAt: createdAt,
            isActive: isActive,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uuid,
            required String name,
            required String key,
            required String userId,
            Value<DateTime> createdAt = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ApiKeysCompanion.insert(
            uuid: uuid,
            name: name,
            key: key,
            userId: userId,
            createdAt: createdAt,
            isActive: isActive,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$ApiKeysTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({userId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (userId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.userId,
                    referencedTable: $$ApiKeysTableReferences._userIdTable(db),
                    referencedColumn:
                        $$ApiKeysTableReferences._userIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$ApiKeysTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ApiKeysTable,
    ApiKey,
    $$ApiKeysTableFilterComposer,
    $$ApiKeysTableOrderingComposer,
    $$ApiKeysTableAnnotationComposer,
    $$ApiKeysTableCreateCompanionBuilder,
    $$ApiKeysTableUpdateCompanionBuilder,
    (ApiKey, $$ApiKeysTableReferences),
    ApiKey,
    PrefetchHooks Function({bool userId})>;
typedef $$ConfigTableCreateCompanionBuilder = ConfigCompanion Function({
  required String id,
  required String value,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$ConfigTableUpdateCompanionBuilder = ConfigCompanion Function({
  Value<String> id,
  Value<String> value,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$ConfigTableFilterComposer
    extends Composer<_$AppDatabase, $ConfigTable> {
  $$ConfigTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$ConfigTableOrderingComposer
    extends Composer<_$AppDatabase, $ConfigTable> {
  $$ConfigTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$ConfigTableAnnotationComposer
    extends Composer<_$AppDatabase, $ConfigTable> {
  $$ConfigTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get value =>
      $composableBuilder(column: $table.value, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$ConfigTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ConfigTable,
    ConfigEntry,
    $$ConfigTableFilterComposer,
    $$ConfigTableOrderingComposer,
    $$ConfigTableAnnotationComposer,
    $$ConfigTableCreateCompanionBuilder,
    $$ConfigTableUpdateCompanionBuilder,
    (ConfigEntry, BaseReferences<_$AppDatabase, $ConfigTable, ConfigEntry>),
    ConfigEntry,
    PrefetchHooks Function()> {
  $$ConfigTableTableManager(_$AppDatabase db, $ConfigTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ConfigTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ConfigTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ConfigTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> value = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ConfigCompanion(
            id: id,
            value: value,
            description: description,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String value,
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ConfigCompanion.insert(
            id: id,
            value: value,
            description: description,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ConfigTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ConfigTable,
    ConfigEntry,
    $$ConfigTableFilterComposer,
    $$ConfigTableOrderingComposer,
    $$ConfigTableAnnotationComposer,
    $$ConfigTableCreateCompanionBuilder,
    $$ConfigTableUpdateCompanionBuilder,
    (ConfigEntry, BaseReferences<_$AppDatabase, $ConfigTable, ConfigEntry>),
    ConfigEntry,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$McpServersTableTableManager get mcpServers =>
      $$McpServersTableTableManager(_db, _db.mcpServers);
  $$OAuthSessionsTableTableManager get oAuthSessions =>
      $$OAuthSessionsTableTableManager(_db, _db.oAuthSessions);
  $$ToolsTableTableManager get tools =>
      $$ToolsTableTableManager(_db, _db.tools);
  $$UsersTableTableManager get users =>
      $$UsersTableTableManager(_db, _db.users);
  $$SessionsTableTableManager get sessions =>
      $$SessionsTableTableManager(_db, _db.sessions);
  $$AccountsTableTableManager get accounts =>
      $$AccountsTableTableManager(_db, _db.accounts);
  $$VerificationsTableTableManager get verifications =>
      $$VerificationsTableTableManager(_db, _db.verifications);
  $$NamespacesTableTableManager get namespaces =>
      $$NamespacesTableTableManager(_db, _db.namespaces);
  $$EndpointsTableTableManager get endpoints =>
      $$EndpointsTableTableManager(_db, _db.endpoints);
  $$NamespaceServerMappingsTableTableManager get namespaceServerMappings =>
      $$NamespaceServerMappingsTableTableManager(
          _db, _db.namespaceServerMappings);
  $$NamespaceToolMappingsTableTableManager get namespaceToolMappings =>
      $$NamespaceToolMappingsTableTableManager(_db, _db.namespaceToolMappings);
  $$ApiKeysTableTableManager get apiKeys =>
      $$ApiKeysTableTableManager(_db, _db.apiKeys);
  $$ConfigTableTableManager get config =>
      $$ConfigTableTableManager(_db, _db.config);
}
