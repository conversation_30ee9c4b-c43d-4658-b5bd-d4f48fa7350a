import 'package:drift/drift.dart';
import 'namespaces.dart';
import 'mcp_servers.dart';
import '../../enums/mcp_server_enums.dart';

@DataClassName('NamespaceServerMapping')
class NamespaceServerMappings extends Table {
  TextColumn get uuid => text().withLength(min: 36, max: 36)();
  TextColumn get namespaceUuid => text().references(Namespaces, #uuid, onDelete: KeyAction.cascade)();
  TextColumn get mcpServerUuid => text().references(McpServers, #uuid, onDelete: KeyAction.cascade)();
  IntColumn get status => intEnum<McpServerStatus>().withDefault(const Constant(0))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<Set<Column>> get uniqueKeys => [
    {namespaceUuid, mcpServerUuid},
  ];
} 