import 'package:drift/drift.dart';
import 'users.dart';

@DataClassName('ApiKey')
class ApiKeys extends Table {
  TextColumn get uuid => text().withLength(min: 36, max: 36)();
  TextColumn get name => text()();
  TextColumn get key => text().unique()();
  TextColumn get userId => text().references(Users, #id, onDelete: KeyAction.cascade)();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<Set<Column>> get uniqueKeys => [
    {userId, name},
  ];
} 