import 'package:drift/drift.dart';
import 'namespaces.dart';

@DataClassName('Endpoint')
class Endpoints extends Table {
  TextColumn get uuid => text().withLength(min: 36, max: 36)();
  TextColumn get name => text().unique()();
  TextColumn get description => text().nullable()();
  TextColumn get namespaceUuid => text().references(Namespaces, #uuid, onDelete: KeyAction.cascade)();
  BoolColumn get enableApiKeyAuth => boolean().withDefault(const Constant(true))();
  BoolColumn get useQueryParamAuth => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {uuid};
} 