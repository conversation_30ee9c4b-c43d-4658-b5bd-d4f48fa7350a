import 'package:drift/drift.dart';

@DataClassName('Verification')
class Verifications extends Table {
  TextColumn get id => text()();
  TextColumn get identifier => text()();
  TextColumn get value => text()();
  DateTimeColumn get expiresAt => dateTime()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {id};
} 