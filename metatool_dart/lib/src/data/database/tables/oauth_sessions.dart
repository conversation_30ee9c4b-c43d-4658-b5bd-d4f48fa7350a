import 'package:drift/drift.dart';
import 'mcp_servers.dart';

@DataClassName('OAuthSession')
class OAuthSessions extends Table {
  TextColumn get uuid => text().withLength(min: 36, max: 36)();
  TextColumn get mcpServerUuid => text().references(McpServers, #uuid, onDelete: KeyAction.cascade)();
  TextColumn get clientInformation => text().nullable()(); // Stored as JSON string
  TextColumn get tokens => text().nullable()(); // Stored as JSON string
  TextColumn get codeVerifier => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<Set<Column>> get uniqueKeys => [
    {mcpServerUuid},
  ];
} 