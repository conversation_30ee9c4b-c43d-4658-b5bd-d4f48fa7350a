import 'package:drift/drift.dart';

@DataClassName('Namespace')
class Namespaces extends Table {
  TextColumn get uuid => text().withLength(min: 36, max: 36)();
  TextColumn get name => text().unique()();
  TextColumn get description => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {uuid};
} 