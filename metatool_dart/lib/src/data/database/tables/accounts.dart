import 'package:drift/drift.dart';
import 'users.dart';

@DataClassName('Account')
class Accounts extends Table {
  TextColumn get id => text()();
  TextColumn get accountId => text()();
  TextColumn get providerId => text()();
  TextColumn get userId => text().references(Users, #id, onDelete: KeyAction.cascade)();
  TextColumn get accessToken => text().nullable()();
  TextColumn get refreshToken => text().nullable()();
  TextColumn get idToken => text().nullable()();
  DateTimeColumn get accessTokenExpiresAt => dateTime().nullable()();
  DateTimeColumn get refreshTokenExpiresAt => dateTime().nullable()();
  TextColumn get scope => text().nullable()();
  TextColumn get password => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {id};
} 