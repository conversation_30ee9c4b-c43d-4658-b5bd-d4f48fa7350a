import 'package:drift/drift.dart';
import '../../enums/mcp_server_enums.dart';
import '../../utils/converters.dart';

@DataClassName('McpServer')
class McpServers extends Table {
  TextColumn get uuid => text().withLength(min: 36, max: 36)();
  
  TextColumn get name => text().withLength(min: 1, max: 255)();
  TextColumn get description => text().nullable()();
  IntColumn get type => intEnum<McpServerType>().withDefault(const Constant(0))();
  
  TextColumn get command => text().nullable()();
  TextColumn get args => text().map(const StringListConverter()).nullable()();
  TextColumn get env => text().map(const JsonMapConverter()).nullable()();
  
  TextColumn get url => text().nullable()();
  TextColumn get bearerToken => text().nullable()();
  
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<Set<Column>> get uniqueKeys => [
    {name},
  ];
} 