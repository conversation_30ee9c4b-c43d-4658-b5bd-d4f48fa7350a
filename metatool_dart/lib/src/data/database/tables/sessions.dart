import 'package:drift/drift.dart';
import 'users.dart';

@DataClassName('Session')
class Sessions extends Table {
  TextColumn get id => text()();
  DateTimeColumn get expiresAt => dateTime()();
  TextColumn get token => text().unique()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  TextColumn get ipAddress => text().nullable()();
  TextColumn get userAgent => text().nullable()();
  TextColumn get userId => text().references(Users, #id, onDelete: KeyAction.cascade)();

  @override
  Set<Column> get primaryKey => {id};
} 