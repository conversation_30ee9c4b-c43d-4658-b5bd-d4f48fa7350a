import 'package:drift/drift.dart';
import 'mcp_servers.dart';

@DataClassName('Tool')
class Tools extends Table {
  TextColumn get uuid => text().withLength(min: 36, max: 36)();
  TextColumn get name => text()();
  TextColumn get description => text().nullable()();
  TextColumn get toolSchema => text()(); // Stored as JSON string
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  TextColumn get mcpServerUuid => text().references(McpServers, #uuid, onDelete: KeyAction.cascade)();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<Set<Column>> get uniqueKeys => [
    {mcpServerUuid, name},
  ];
} 