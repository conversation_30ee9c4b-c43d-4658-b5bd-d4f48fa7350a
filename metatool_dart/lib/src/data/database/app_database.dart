import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import 'tables/mcp_servers.dart';
import 'tables/oauth_sessions.dart';
import 'tables/tools.dart';
import 'tables/users.dart';
import 'tables/sessions.dart';
import 'tables/accounts.dart';
import 'tables/verifications.dart';
import 'tables/namespaces.dart';
import 'tables/endpoints.dart';
import 'tables/namespace_server_mappings.dart';
import 'tables/namespace_tool_mappings.dart';
import 'tables/api_keys.dart';
import 'tables/config.dart';

part 'app_database.g.dart';

@DriftDatabase(tables: [
  McpServers,
  OAuthSessions,
  Tools,
  Users,
  Sessions,
  Accounts,
  Verifications,
  Namespaces,
  Endpoints,
  NamespaceServerMappings,
  NamespaceToolMappings,
  ApiKeys,
  Config,
])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'metamcp.db'));

    return NativeDatabase.createInBackground(
      file,
      setup: (database) {
        database.execute('PRAGMA foreign_keys = ON');
        database.execute('PRAGMA journal_mode = WAL');
        database.execute('PRAGMA synchronous = NORMAL');
      },
    );
  });
} 