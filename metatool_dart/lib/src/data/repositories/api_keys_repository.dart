import 'package:drift/drift.dart';
import 'package:nanoid/nanoid.dart';
import '../database/app_database.dart';
import '../models/api_key_model.dart';
import '../exceptions/database_exception.dart';
import '../utils/uuid.dart';

class ApiKeysRepository {
  final AppDatabase db;

  ApiKeysRepository(this.db);

  String _generateApiKey() {
    return 'sk_mt_${customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 64)}';
  }

  Future<ApiKeyModel> create({required String name, required String userId, bool isActive = true}) async {
    final model = ApiKeyModel(
      uuid: uuid.v4(),
      name: name,
      key: _generateApiKey(),
      userId: userId,
      createdAt: DateTime.now(),
      isActive: isActive,
    );
    final companion = model.toCompanion();
    final drift = await db.into(db.apiKeys).insertReturning(companion);
    return ApiKeyModel.fromDrift(drift);
  }

  Future<List<ApiKeyModel>> findByUserId(String userId) async {
    final query = db.select(db.apiKeys)
      ..where((t) => t.userId.equals(userId))
      ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]);
    final results = await query.get();
    return results.map((d) => ApiKeyModel.fromDrift(d)).toList();
  }

  Future<ApiKeyModel?> findByUuid(String uuid, String userId) async {
    final query = db.select(db.apiKeys)
      ..where((t) => t.uuid.equals(uuid) & t.userId.equals(userId));
    final drift = await query.getSingleOrNull();
    return drift != null ? ApiKeyModel.fromDrift(drift) : null;
  }
  
  Future<({bool valid, String? userId, String? keyUuid})> validateApiKey(String key) async {
      final query = db.select(db.apiKeys)..where((t) => t.key.equals(key));
      final drift = await query.getSingleOrNull();
      
      if (drift == null || !drift.isActive) {
          return (valid: false, userId: null, keyUuid: null);
      }
      
      return (valid: true, userId: drift.userId, keyUuid: drift.uuid);
  }

  Future<ApiKeyModel> update({required String uuid, required String userId, String? name, bool? isActive}) async {
    final companion = ApiKeysCompanion(
      name: name != null ? Value(name) : const Value.absent(),
      isActive: isActive != null ? Value(isActive) : const Value.absent(),
    );
    
    final updatedList = await (db.update(db.apiKeys)..where((t) => t.uuid.equals(uuid) & t.userId.equals(userId))).writeReturning(companion);
    
    if(updatedList.isEmpty) {
        throw NotFoundException("API Key not found");
    }
    return ApiKeyModel.fromDrift(updatedList.first);
  }

  Future<bool> delete(String uuid, String userId) async {
    final deletedRows = await (db.delete(db.apiKeys)..where((t) => t.uuid.equals(uuid) & t.userId.equals(userId))).go();
    return deletedRows > 0;
  }
} 