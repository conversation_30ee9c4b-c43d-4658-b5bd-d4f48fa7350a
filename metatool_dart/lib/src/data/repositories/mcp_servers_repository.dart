import 'package:drift/drift.dart';
import 'package:sqlite3/sqlite3.dart';

import '../database/app_database.dart';
import '../enums/mcp_server_enums.dart';
import '../exceptions/database_exception.dart';
import '../models/mcp_server_model.dart';

// Define the return type using a record. This makes the return type explicit and safe.
typedef McpServerResultTuple = ({McpServerModel server, McpServerStatus status});

class McpServersRepository {
  final AppDatabase db;

  McpServersRepository(this.db);

  Future<McpServerModel> create(McpServerModel model) async {
    try {
      final companion = model.toCompanion();
      final drift = await db.into(db.mcpServers).insertReturning(companion);
      return McpServerModel.fromDrift(drift);
    } on SqliteException catch (e) {
      throw _handleSqliteError(e, model.name);
    }
  }

  Future<McpServerModel?> findById(String uuid) async {
    final query = db.select(db.mcpServers)..where((t) => t.uuid.equals(uuid));
    final drift = await query.getSingleOrNull();
    return drift != null ? McpServerModel.fromDrift(drift) : null;
  }

  Future<McpServerModel?> findByName(String name) async {
    final query = db.select(db.mcpServers)..where((t) => t.name.equals(name));
    final drift = await query.getSingleOrNull();
    return drift != null ? McpServerModel.fromDrift(drift) : null;
  }

  Future<List<McpServerModel>> findAll() async {
    final query = db.select(db.mcpServers)
      ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]);
    final results = await query.get();
    return results.map((d) => McpServerModel.fromDrift(d)).toList();
  }

  Future<List<McpServerModel>> findByType(McpServerType type) async {
    final query = db.select(db.mcpServers)..where((t) => t.type.equals(type.index));
    final results = await query.get();
    return results.map((d) => McpServerModel.fromDrift(d)).toList();
  }

  Future<McpServerModel> update(McpServerModel model) async {
    try {
      // Drift does not automatically update `updatedAt` fields.
      // We can create a new companion with the updated time.
      final companion = model.toCompanion();
      
      final updated = await (db.update(db.mcpServers)..where((t) => t.uuid.equals(model.uuid)))
          .writeReturning(companion);
      
      if (updated.isEmpty) {
        throw NotFoundException('MCP Server with uuid ${model.uuid} not found');
      }
      
      return McpServerModel.fromDrift(updated.first);
    } on SqliteException catch (e) {
      throw _handleSqliteError(e, model.name);
    }
  }

  Future<bool> deleteById(String uuid) async {
    final deletedRows = await (db.delete(db.mcpServers)..where((t) => t.uuid.equals(uuid))).go();
    return deletedRows > 0;
  }

  Future<List<McpServerResultTuple>> getServersByNamespace(
    String namespaceUuid, {
    bool includeInactive = false,
  }) async {
    // The base of the query is mcpServers table
    final query = db.select(db.mcpServers).join([
      // We join it with the mappings table
      innerJoin(
        db.namespaceServerMappings,
        db.namespaceServerMappings.mcpServerUuid.equalsExp(db.mcpServers.uuid),
      ),
    ]);

    // We filter by the provided namespace UUID
    query.where(db.namespaceServerMappings.namespaceUuid.equals(namespaceUuid));

    // Optionally, we also filter by status
    if (!includeInactive) {
      query.where(db.namespaceServerMappings.status.equals(McpServerStatus.active.index));
    }

    // Execute the query
    final results = await query.get();

    // Map the raw TypedResult to our custom record tuple
    return results.map((typedResult) {
      final serverData = typedResult.readTable(db.mcpServers);
      final mappingData = typedResult.readTable(db.namespaceServerMappings);
      return (
        server: McpServerModel.fromDrift(serverData),
        status: mappingData.status,
      );
    }).toList();
  }

  Future<List<McpServerModel>> bulkCreate(List<McpServerModel> models) async {
    final results = <McpServerModel>[];
    
    await db.transaction(() async {
      for (final model in models) {
        try {
          final companion = model.toCompanion();
          final drift = await db.into(db.mcpServers).insertReturning(companion);
          results.add(McpServerModel.fromDrift(drift));
        } on SqliteException catch (e) {
          throw _handleSqliteError(e, model.name);
        }
      }
    });
    
    return results;
  }

  DatabaseException _handleSqliteError(SqliteException e, String? serverName) {
    if (e.extendedResultCode == 2067) { // SQLITE_CONSTRAINT_UNIQUE
      return UniqueConstraintException(
        'Server name "$serverName" already exists. Server names must be unique.',
        'name',
      );
    }
    
    return DatabaseException(
      'Failed to process MCP server: ${e.message}',
      code: e.resultCode.toString(),
      cause: e,
    );
  }
} 