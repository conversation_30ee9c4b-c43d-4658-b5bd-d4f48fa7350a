import 'package:drift/drift.dart';
import '../database/app_database.dart';
import '../models/verification_model.dart';

class VerificationsRepository {
  final AppDatabase db;
  VerificationsRepository(this.db);

  Future<VerificationModel> create(VerificationModel model) async {
    final companion = model.toCompanion();
    final result = await db.into(db.verifications).insertReturning(companion);
    return VerificationModel.fromDrift(result);
  }

  Future<VerificationModel?> findByIdentifier(String identifier, String value) async {
    final query = db.select(db.verifications)
      ..where((tbl) => tbl.identifier.equals(identifier) & tbl.value.equals(value));
    final result = await query.getSingleOrNull();
    return result != null ? VerificationModel.fromDrift(result) : null;
  }
} 