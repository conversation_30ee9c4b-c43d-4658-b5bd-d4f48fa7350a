
import '../database/app_database.dart';
import '../models/config_model.dart';

class ConfigRepository {
  final AppDatabase db;

  ConfigRepository(this.db);

  Future<ConfigModel?> getConfig(String id) async {
    final query = db.select(db.config)..where((t) => t.id.equals(id));
    final drift = await query.getSingleOrNull();
    return drift != null ? ConfigModel.fromDrift(drift) : null;
  }
  
  Future<void> setConfig({required String id, required String value, String? description}) async {
    final model = ConfigModel(
      id: id,
      value: value,
      description: description,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    await db.into(db.config).insertOnConflictUpdate(model.toCompanion());
  }

  Future<List<ConfigModel>> getAllConfigs() async {
    final results = await db.select(db.config).get();
    return results.map((d) => ConfigModel.fromDrift(d)).toList();
  }
  
  Future<bool> deleteConfig(String id) async {
    final deletedRows = await (db.delete(db.config)..where((t) => t.id.equals(id))).go();
    return deletedRows > 0;
  }
} 