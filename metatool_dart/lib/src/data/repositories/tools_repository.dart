import '../database/app_database.dart';
import '../models/tool_model.dart';

class ToolsRepository {
  final AppDatabase db;

  ToolsRepository(this.db);

  Future<ToolModel> create(ToolModel model) async {
    final companion = model.toCompanion();
    final drift = await db.into(db.tools).insertReturning(companion);
    return ToolModel.fromDrift(drift);
  }

  Future<List<ToolModel>> findByMcpServerUuid(String mcpServerUuid) async {
    final query = db.select(db.tools)..where((t) => t.mcpServerUuid.equals(mcpServerUuid));
    final results = await query.get();
    return results.map((d) => ToolModel.fromDrift(d)).toList();
  }

  Future<List<ToolModel>> bulkUpsert(String mcpServerUuid, List<ToolModel> models) async {
     final companions = models.map((m) => m.toCompanion()).toList();
     await db.batch((batch) {
       batch.insertAllOnConflictUpdate(db.tools, companions);
     });
     // Note: This won't return the created/updated models directly with SQLite.
     // We might need to re-fetch them if needed.
     return findByMcpServerUuid(mcpServerUuid);
  }
  
  Future<ToolModel?> findByUuid(String uuid) async {
    final query = db.select(db.tools)..where((t) => t.uuid.equals(uuid));
    final drift = await query.getSingleOrNull();
    return drift != null ? ToolModel.fromDrift(drift) : null;
  }
  
  Future<bool> deleteByUuid(String uuid) async {
     final deletedRows = await (db.delete(db.tools)..where((t) => t.uuid.equals(uuid))).go();
     return deletedRows > 0;
  }
} 