import 'package:drift/drift.dart';
import '../database/app_database.dart';
import '../enums/mcp_server_enums.dart';
import '../utils/uuid.dart';

class NamespaceMappingsRepository {
  final AppDatabase db;
  NamespaceMappingsRepository(this.db);
  
  Future<void> updateServerStatus(String namespaceUuid, String serverUuid, McpServerStatus status) async {
    await (db.update(db.namespaceServerMappings)
      ..where((tbl) => tbl.namespaceUuid.equals(namespaceUuid) & tbl.mcpServerUuid.equals(serverUuid)))
      .write(NamespaceServerMappingsCompanion(status: Value(status)));
  }

  Future<void> updateToolStatus(String namespaceUuid, String toolUuid, McpServerStatus status) async {
    await (db.update(db.namespaceToolMappings)
      ..where((tbl) => tbl.namespaceUuid.equals(namespaceUuid) & tbl.toolUuid.equals(toolUuid)))
      .write(NamespaceToolMappingsCompanion(status: Value(status)));
  }

  Future<McpServerStatus?> getToolStatus(
      String namespaceUuid, String toolName, String serverUuid) async {
    final query = db.select(db.namespaceToolMappings).join([
      innerJoin(
        db.tools,
        db.tools.uuid.equalsExp(db.namespaceToolMappings.toolUuid),
      ),
    ]);

    query.where(
      db.namespaceToolMappings.namespaceUuid.equals(namespaceUuid) &
          db.namespaceToolMappings.mcpServerUuid.equals(serverUuid) &
          db.tools.name.equals(toolName),
    );

    query.limit(1);

    final result = await query.getSingleOrNull();

    if (result != null) {
      final mapping = result.readTable(db.namespaceToolMappings);
      return mapping.status;
    }

    return null;
  }

  Future<void> bulkUpsertNamespaceToolMappings(String namespaceUuid, List<({String toolUuid, String serverUuid, McpServerStatus status})> mappings) async {
    if (mappings.isEmpty) return;
    
    final companions = mappings.map((m) => NamespaceToolMappingsCompanion.insert(
      uuid: uuid.v4(),
      namespaceUuid: namespaceUuid,
      toolUuid: m.toolUuid,
      mcpServerUuid: m.serverUuid,
      status: Value(m.status),
    )).toList();

    await db.batch((batch) {
      batch.insertAllOnConflictUpdate(db.namespaceToolMappings, companions);
    });
  }
} 