import 'package:drift/drift.dart';
import '../utils/uuid.dart';
import '../database/app_database.dart';
import '../models/namespace_model.dart';
import '../exceptions/database_exception.dart';

class NamespacesRepository {
  final AppDatabase db;

  NamespacesRepository(this.db);

  Future<NamespaceModel> create(NamespaceModel model, {List<String> mcpServerUuids = const []}) async {
    return db.transaction(() async {
      final namespaceCompanion = model.toCompanion();
      final createdNamespace = await db.into(db.namespaces).insertReturning(namespaceCompanion);

      if (mcpServerUuids.isNotEmpty) {
        final serverMappings = mcpServerUuids.map((serverUuid) =>
            NamespaceServerMappingsCompanion.insert(
                uuid: uuid.v4(),
                namespaceUuid: createdNamespace.uuid,
                mcpServerUuid: serverUuid,
            ));
        await db.batch((batch) => batch.insertAll(db.namespaceServerMappings, serverMappings));
      }

      return NamespaceModel.fromDrift(createdNamespace);
    });
  }

  Future<List<NamespaceModel>> findAll() async {
    final results = await db.select(db.namespaces).get();
    return results.map((d) => NamespaceModel.fromDrift(d)).toList();
  }

  Future<NamespaceModel?> findByUuid(String uuid) async {
    final query = db.select(db.namespaces)..where((t) => t.uuid.equals(uuid));
    final drift = await query.getSingleOrNull();
    return drift != null ? NamespaceModel.fromDrift(drift) : null;
  }
  
  Future<List<McpServer>> findServersByNamespace(String namespaceUuid) async {
      final query = db.select(db.namespaceServerMappings).join([
          innerJoin(db.mcpServers, db.mcpServers.uuid.equalsExp(db.namespaceServerMappings.mcpServerUuid))
      ])
      ..where(db.namespaceServerMappings.namespaceUuid.equals(namespaceUuid));

      final results = await query.get();
      return results.map((row) => row.readTable(db.mcpServers)).toList();
  }

  Future<bool> deleteByUuid(String uuid) async {
    final deletedRows = await (db.delete(db.namespaces)..where((t) => t.uuid.equals(uuid))).go();
    return deletedRows > 0;
  }

  Future<NamespaceModel> update(NamespaceModel model, {List<String>? mcpServerUuids}) async {
      return db.transaction(() async {
          final companion = model.toCompanion().copyWith(updatedAt: Value(DateTime.now()));
          final updatedList = await (db.update(db.namespaces)..where((t) => t.uuid.equals(model.uuid))).writeReturning(companion);
          
          if (updatedList.isEmpty) {
              throw NotFoundException("Namespace with uuid ${model.uuid} not found");
          }

          if (mcpServerUuids != null) {
              await (db.delete(db.namespaceServerMappings)..where((t) => t.namespaceUuid.equals(model.uuid))).go();
              if (mcpServerUuids.isNotEmpty) {
                  final serverMappings = mcpServerUuids.map((serverUuid) =>
                      NamespaceServerMappingsCompanion.insert(
                          uuid: uuid.v4(),
                          namespaceUuid: model.uuid,
                          mcpServerUuid: serverUuid,
                      ));
                  await db.batch((batch) => batch.insertAll(db.namespaceServerMappings, serverMappings));
              }
          }
          return NamespaceModel.fromDrift(updatedList.first);
      });
  }
} 