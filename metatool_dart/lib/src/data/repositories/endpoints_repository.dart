import 'package:drift/drift.dart';
import '../database/app_database.dart';
import '../models/endpoint_model.dart';
import '../exceptions/database_exception.dart';

class EndpointsRepository {
  final AppDatabase db;

  EndpointsRepository(this.db);

  Future<EndpointModel> create(EndpointModel model) async {
    final companion = model.toCompanion();
    final drift = await db.into(db.endpoints).insertReturning(companion);
    return EndpointModel.fromDrift(drift);
  }
  
  Future<List<EndpointModel>> findAll() async {
      final results = await db.select(db.endpoints).get();
      return results.map((d) => EndpointModel.fromDrift(d)).toList();
  }
  
  Future<EndpointModel?> findByUuid(String uuid) async {
    final query = db.select(db.endpoints)..where((t) => t.uuid.equals(uuid));
    final drift = await query.getSingleOrNull();
    return drift != null ? EndpointModel.fromDrift(drift) : null;
  }
  
  Future<EndpointModel?> findByName(String name) async {
    final query = db.select(db.endpoints)..where((t) => t.name.equals(name));
    final drift = await query.getSingleOrNull();
    return drift != null ? EndpointModel.fromDrift(drift) : null;
  }

  Future<bool> deleteByUuid(String uuid) async {
    final deletedRows = await (db.delete(db.endpoints)..where((t) => t.uuid.equals(uuid))).go();
    return deletedRows > 0;
  }

  Future<EndpointModel> update(EndpointModel model) async {
    final companion = model.toCompanion().copyWith(updatedAt: Value(DateTime.now()));
    final updatedList = await (db.update(db.endpoints)..where((t) => t.uuid.equals(model.uuid))).writeReturning(companion);
    
    if (updatedList.isEmpty) {
        throw NotFoundException("Endpoint with uuid ${model.uuid} not found");
    }
    return EndpointModel.fromDrift(updatedList.first);
  }
} 