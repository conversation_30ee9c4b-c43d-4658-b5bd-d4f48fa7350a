import 'package:drift/drift.dart';
import '../database/app_database.dart';
import '../models/oauth_session_model.dart';
import '../exceptions/database_exception.dart';

class OAuthSessionsRepository {
  final AppDatabase db;

  OAuthSessionsRepository(this.db);

  Future<OAuthSessionModel?> findByMcpServerUuid(String mcpServerUuid) async {
    final query = db.select(db.oAuthSessions)..where((t) => t.mcpServerUuid.equals(mcpServerUuid));
    final drift = await query.getSingleOrNull();
    return drift != null ? OAuthSessionModel.fromDrift(drift) : null;
  }
  
  Future<OAuthSessionModel> create(OAuthSessionModel model) async {
      final drift = await db.into(db.oAuthSessions).insertReturning(model.toCompanion());
      return OAuthSessionModel.fromDrift(drift);
  }
  
  Future<OAuthSessionModel> update(OAuthSessionModel model) async {
      final companion = model.toCompanion().copyWith(updatedAt: Value(DateTime.now()));
      final updatedList = await (db.update(db.oAuthSessions)..where((t) => t.mcpServerUuid.equals(model.mcpServerUuid))).writeReturning(companion);

      if (updatedList.isEmpty) {
          throw NotFoundException("OAuthSession for server ${model.mcpServerUuid} not found");
      }
      return OAuthSessionModel.fromDrift(updatedList.first);
  }

  Future<OAuthSessionModel> upsert(OAuthSessionModel model) async {
    final existing = await findByMcpServerUuid(model.mcpServerUuid);
    if (existing != null) {
      return update(model);
    } else {
      return create(model);
    }
  }

  Future<bool> deleteByMcpServerUuid(String mcpServerUuid) async {
    final deletedRows = await (db.delete(db.oAuthSessions)..where((t) => t.mcpServerUuid.equals(mcpServerUuid))).go();
    return deletedRows > 0;
  }
} 