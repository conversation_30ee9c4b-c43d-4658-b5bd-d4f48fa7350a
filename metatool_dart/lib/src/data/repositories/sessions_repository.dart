
import '../database/app_database.dart';
import '../models/session_model.dart';

class SessionsRepository {
  final AppDatabase db;
  SessionsRepository(this.db);

  Future<SessionModel> create(SessionModel model) async {
    final companion = model.toCompanion();
    final result = await db.into(db.sessions).insertReturning(companion);
    return SessionModel.fromDrift(result);
  }

  Future<SessionModel?> findByToken(String token) async {
    final query = db.select(db.sessions)..where((tbl) => tbl.token.equals(token));
    final result = await query.getSingleOrNull();
    return result != null ? SessionModel.fromDrift(result) : null;
  }

  Future<bool> deleteByToken(String token) async {
    final count = await (db.delete(db.sessions)..where((tbl) => tbl.token.equals(token))).go();
    return count > 0;
  }
} 