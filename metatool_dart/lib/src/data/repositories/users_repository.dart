
import '../database/app_database.dart';
import '../models/user_model.dart';

class UsersRepository {
  final AppDatabase db;
  UsersRepository(this.db);

  Future<UserModel> create(UserModel model) async {
    final companion = model.toCompanion();
    final result = await db.into(db.users).insertReturning(companion);
    return UserModel.fromDrift(result);
  }

  Future<UserModel?> findById(String id) async {
    final query = db.select(db.users)..where((tbl) => tbl.id.equals(id));
    final result = await query.getSingleOrNull();
    return result != null ? UserModel.fromDrift(result) : null;
  }
  
  Future<UserModel?> findByEmail(String email) async {
    final query = db.select(db.users)..where((tbl) => tbl.email.equals(email));
    final result = await query.getSingleOrNull();
    return result != null ? UserModel.fromDrift(result) : null;
  }
} 