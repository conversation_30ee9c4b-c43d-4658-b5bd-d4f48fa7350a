import 'package:drift/drift.dart';
import '../database/app_database.dart';
import '../models/account_model.dart';

class AccountsRepository {
  final AppDatabase db;
  AccountsRepository(this.db);

  Future<AccountModel> create(AccountModel model) async {
    final companion = model.toCompanion();
    final result = await db.into(db.accounts).insertReturning(companion);
    return AccountModel.fromDrift(result);
  }

  Future<AccountModel?> findByProvider(String providerId, String accountId) async {
    final query = db.select(db.accounts)
      ..where((tbl) => tbl.providerId.equals(providerId) & tbl.accountId.equals(accountId));
    final result = await query.getSingleOrNull();
    return result != null ? AccountModel.fromDrift(result) : null;
  }
} 