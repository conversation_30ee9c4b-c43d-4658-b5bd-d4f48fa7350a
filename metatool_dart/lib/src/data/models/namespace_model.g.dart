// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'namespace_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NamespaceModel _$NamespaceModelFromJson(Map<String, dynamic> json) =>
    NamespaceModel(
      uuid: json['uuid'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$NamespaceModelToJson(NamespaceModel instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'name': instance.name,
      'description': instance.description,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
