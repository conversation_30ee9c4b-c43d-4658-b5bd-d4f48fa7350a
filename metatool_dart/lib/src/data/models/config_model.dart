import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'config_model.g.dart';

@JsonSerializable()
class ConfigModel {
  final String id;
  final String value;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  ConfigModel({
    required this.id,
    required this.value,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) =>
      _$ConfigModelFromJson(json);
  Map<String, dynamic> toJson() => _$ConfigModelToJson(this);

  factory ConfigModel.fromDrift(ConfigEntry drift) {
    return ConfigModel(
      id: drift.id,
      value: drift.value,
      description: drift.description,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
    );
  }

  ConfigCompanion toCompanion() {
    return ConfigCompanion(
      id: Value(id),
      value: Value(value),
      description: Value(description),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }
} 