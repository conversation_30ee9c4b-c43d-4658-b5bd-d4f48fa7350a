// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'endpoint_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EndpointModel _$EndpointModelFromJson(Map<String, dynamic> json) =>
    EndpointModel(
      uuid: json['uuid'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      namespaceUuid: json['namespaceUuid'] as String,
      enableApiKeyAuth: json['enableApiKeyAuth'] as bool,
      useQueryParamAuth: json['useQueryParamAuth'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$EndpointModelToJson(EndpointModel instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'name': instance.name,
      'description': instance.description,
      'namespaceUuid': instance.namespaceUuid,
      'enableApiKeyAuth': instance.enableApiKeyAuth,
      'useQueryParamAuth': instance.useQueryParamAuth,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
