import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'endpoint_model.g.dart';

@JsonSerializable()
class EndpointModel {
  final String uuid;
  final String name;
  final String? description;
  final String namespaceUuid;
  final bool enableApiKeyAuth;
  final bool useQueryParamAuth;
  final DateTime createdAt;
  final DateTime updatedAt;

  EndpointModel({
    required this.uuid,
    required this.name,
    this.description,
    required this.namespaceUuid,
    required this.enableApiKeyAuth,
    required this.useQueryParamAuth,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EndpointModel.fromJson(Map<String, dynamic> json) =>
      _$EndpointModelFromJson(json);
  Map<String, dynamic> toJson() => _$EndpointModelToJson(this);

  factory EndpointModel.fromDrift(Endpoint drift) {
    return EndpointModel(
      uuid: drift.uuid,
      name: drift.name,
      description: drift.description,
      namespaceUuid: drift.namespaceUuid,
      enableApiKeyAuth: drift.enableApiKeyAuth,
      useQueryParamAuth: drift.useQueryParamAuth,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
    );
  }

  EndpointsCompanion toCompanion() {
    return EndpointsCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: Value(description),
      namespaceUuid: Value(namespaceUuid),
      enableApiKeyAuth: Value(enableApiKeyAuth),
      useQueryParamAuth: Value(useQueryParamAuth),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }
} 