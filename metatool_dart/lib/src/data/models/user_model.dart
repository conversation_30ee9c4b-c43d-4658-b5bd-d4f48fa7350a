import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String name;
  final String email;
  final bool emailVerified;
  final String? image;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.emailVerified,
    this.image,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  factory UserModel.fromDrift(User drift) {
    return UserModel(
      id: drift.id,
      name: drift.name,
      email: drift.email,
      emailVerified: drift.emailVerified,
      image: drift.image,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
    );
  }

  UsersCompanion toCompanion() {
    return UsersCompanion(
      id: Value(id),
      name: Value(name),
      email: Value(email),
      emailVerified: Value(emailVerified),
      image: Value(image),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }
} 