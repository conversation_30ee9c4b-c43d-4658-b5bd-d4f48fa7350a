import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'api_key_model.g.dart';

@JsonSerializable()
class ApiKeyModel {
  final String uuid;
  final String name;
  final String key;
  final String userId;
  final DateTime createdAt;
  final bool isActive;

  ApiKeyModel({
    required this.uuid,
    required this.name,
    required this.key,
    required this.userId,
    required this.createdAt,
    required this.isActive,
  });

  factory ApiKeyModel.fromJson(Map<String, dynamic> json) =>
      _$ApiKeyModelFromJson(json);
  Map<String, dynamic> toJson() => _$ApiKeyModelToJson(this);

  factory ApiKeyModel.fromDrift(ApiKey drift) {
    return ApiKeyModel(
      uuid: drift.uuid,
      name: drift.name,
      key: drift.key,
      userId: drift.userId,
      createdAt: drift.createdAt,
      isActive: drift.isActive,
    );
  }

  ApiKeysCompanion toCompanion() {
    return ApiKeysCompanion(
      uuid: Value(uuid),
      name: Value(name),
      key: Value(key),
      userId: Value(userId),
      createdAt: Value(createdAt),
      isActive: Value(isActive),
    );
  }
} 