// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mcp_server_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

McpServerModel _$McpServerModelFromJson(Map<String, dynamic> json) =>
    McpServerModel(
      uuid: json['uuid'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: $enumDecode(_$McpServerTypeEnumMap, json['type']),
      command: json['command'] as String?,
      args:
          (json['args'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      env: (json['env'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
      url: json['url'] as String?,
      bearerToken: json['bearerToken'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$McpServerModelToJson(McpServerModel instance) =>
    <String, dynamic>{
      'uuid': instance.uuid,
      'name': instance.name,
      'description': instance.description,
      'type': _$McpServerTypeEnumMap[instance.type]!,
      'command': instance.command,
      'args': instance.args,
      'env': instance.env,
      'url': instance.url,
      'bearerToken': instance.bearerToken,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$McpServerTypeEnumMap = {
  McpServerType.stdio: 'stdio',
  McpServerType.sse: 'sse',
  McpServerType.streamableHttp: 'streamableHttp',
};
