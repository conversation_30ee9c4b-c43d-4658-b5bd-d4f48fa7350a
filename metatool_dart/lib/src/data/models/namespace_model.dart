import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'namespace_model.g.dart';

@JsonSerializable()
class NamespaceModel {
  final String uuid;
  final String name;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  NamespaceModel({
    required this.uuid,
    required this.name,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NamespaceModel.fromJson(Map<String, dynamic> json) =>
      _$NamespaceModelFromJson(json);
  Map<String, dynamic> toJson() => _$NamespaceModelToJson(this);

  factory NamespaceModel.fromDrift(Namespace drift) {
    return NamespaceModel(
      uuid: drift.uuid,
      name: drift.name,
      description: drift.description,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
    );
  }

  NamespacesCompanion toCompanion() {
    return NamespacesCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: Value(description),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }
} 