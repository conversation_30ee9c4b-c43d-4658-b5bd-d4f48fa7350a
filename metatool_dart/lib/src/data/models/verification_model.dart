import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'verification_model.g.dart';

@JsonSerializable()
class VerificationModel {
  final String id;
  final String identifier;
  final String value;
  final DateTime expiresAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  VerificationModel({
    required this.id,
    required this.identifier,
    required this.value,
    required this.expiresAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory VerificationModel.fromJson(Map<String, dynamic> json) =>
      _$VerificationModelFromJson(json);
  Map<String, dynamic> toJson() => _$VerificationModelToJson(this);

  factory VerificationModel.fromDrift(Verification drift) {
    return VerificationModel(
      id: drift.id,
      identifier: drift.identifier,
      value: drift.value,
      expiresAt: drift.expiresAt,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
    );
  }
  
  VerificationsCompanion toCompanion() {
    return VerificationsCompanion(
      id: Value(id),
      identifier: Value(identifier),
      value: Value(value),
      expiresAt: Value(expiresAt),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }
} 