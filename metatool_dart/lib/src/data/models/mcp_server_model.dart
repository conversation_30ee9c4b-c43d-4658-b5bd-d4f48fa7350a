import 'package:drift/drift.dart' hide Json<PERSON>ey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';
import '../enums/mcp_server_enums.dart';
import '../utils/converters.dart';

part 'mcp_server_model.g.dart';

@JsonSerializable()
class McpServerModel {
  final String uuid;
  final String name;
  final String? description;
  final McpServerType type;
  final String? command;

  @StringListConverter()
  final List<String> args;

  @JsonMapConverter()
  final Map<String, String> env;
  
  final String? url;
  final String? bearerToken;
  final DateTime createdAt;

  McpServerModel({
    required this.uuid,
    required this.name,
    this.description,
    required this.type,
    this.command,
    this.args = const [],
    this.env = const {},
    this.url,
    this.bearerToken,
    required this.createdAt,
  });

  factory McpServerModel.fromJson(Map<String, dynamic> json) =>
      _$McpServerModelFromJson(json);

  Map<String, dynamic> toJson() => _$McpServerModelToJson(this);

  factory McpServerModel.fromDrift(McpServer drift) {
    return McpServerModel(
      uuid: drift.uuid,
      name: drift.name,
      description: drift.description,
      type: drift.type,
      command: drift.command,
      args: drift.args ?? [],
      env: drift.env ?? {},
      url: drift.url,
      bearerToken: drift.bearerToken,
      createdAt: drift.createdAt,
    );
  }

  McpServersCompanion toCompanion() {
    return McpServersCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: Value(description),
      type: Value(type),
      command: Value(command),
      args: Value(args),
      env: Value(env),
      url: Value(url),
      bearerToken: Value(bearerToken),
      createdAt: Value(createdAt),
    );
  }
} 