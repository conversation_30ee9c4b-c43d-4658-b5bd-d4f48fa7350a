import 'package:drift/drift.dart' hide Json<PERSON>ey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'session_model.g.dart';

@JsonSerializable()
class SessionModel {
  final String id;
  final DateTime expiresAt;
  final String token;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? ipAddress;
  final String? userAgent;
  final String userId;

  SessionModel({
    required this.id,
    required this.expiresAt,
    required this.token,
    required this.createdAt,
    required this.updatedAt,
    this.ipAddress,
    this.userAgent,
    required this.userId,
  });
  
  factory SessionModel.fromJson(Map<String, dynamic> json) => _$SessionModelFromJson(json);
  Map<String, dynamic> toJson() => _$SessionModelToJson(this);

  factory SessionModel.fromDrift(Session drift) {
    return SessionModel(
      id: drift.id,
      expiresAt: drift.expiresAt,
      token: drift.token,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
      ipAddress: drift.ipAddress,
      userAgent: drift.userAgent,
      userId: drift.userId,
    );
  }
  
  SessionsCompanion toCompanion() {
    return SessionsCompanion(
      id: Value(id),
      expiresAt: Value(expiresAt),
      token: Value(token),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      ipAddress: Value(ipAddress),
      userAgent: Value(userAgent),
      userId: Value(userId),
    );
  }
} 