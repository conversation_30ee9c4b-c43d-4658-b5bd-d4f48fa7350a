import 'dart:convert';
import 'package:drift/drift.dart' hide J<PERSON><PERSON>ey;
import 'package:json_annotation/json_annotation.dart';
import 'package:mcp_dart/mcp_dart.dart' as mcp;
import 'package:uuid/uuid.dart';
import '../database/app_database.dart';

part 'tool_model.g.dart';

final _uuid = Uuid();

@JsonSerializable()
class ToolModel {
  final String uuid;
  final String name;
  final String? description;
  final Map<String, dynamic> toolSchema;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String mcpServerUuid;

  ToolModel({
    required this.uuid,
    required this.name,
    this.description,
    required this.toolSchema,
    required this.createdAt,
    required this.updatedAt,
    required this.mcpServerUuid,
  });

  factory ToolModel.fromMcpTool(mcp.Tool mcpTool, String serverUuid) {
    final now = DateTime.now();
    final schema = mcpTool.inputSchema;
    // Manually create a Map from the schema, as it might not have a toJson method.
    final schemaMap = schema != null
        ? {
            'type': schema.type,
            if (schema.properties != null) 'properties': schema.properties,
            if (schema.required != null) 'required': schema.required,
          }
        : <String, dynamic>{};

    return ToolModel(
      uuid: _uuid.v4(),
      name: mcpTool.name,
      description: mcpTool.description,
      toolSchema: schemaMap,
      createdAt: now,
      updatedAt: now,
      mcpServerUuid: serverUuid,
    );
  }

  factory ToolModel.fromJson(Map<String, dynamic> json) => _$ToolModelFromJson(json);
  Map<String, dynamic> toJson() => _$ToolModelToJson(this);

  factory ToolModel.fromDrift(Tool drift) {
    return ToolModel(
      uuid: drift.uuid,
      name: drift.name,
      description: drift.description,
      toolSchema: json.decode(drift.toolSchema),
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
      mcpServerUuid: drift.mcpServerUuid,
    );
  }

  ToolsCompanion toCompanion() {
    return ToolsCompanion(
      uuid: Value(uuid),
      name: Value(name),
      description: Value(description),
      toolSchema: Value(json.encode(toolSchema)),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      mcpServerUuid: Value(mcpServerUuid),
    );
  }
} 