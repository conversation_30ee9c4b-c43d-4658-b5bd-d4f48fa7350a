import 'dart:convert';
import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'oauth_session_model.g.dart';

@JsonSerializable()
class OAuthSessionModel {
  final String uuid;
  final String mcpServerUuid;
  final Map<String, dynamic>? clientInformation;
  final Map<String, dynamic>? tokens;
  final String? codeVerifier;
  final DateTime createdAt;
  final DateTime updatedAt;

  OAuthSessionModel({
    required this.uuid,
    required this.mcpServerUuid,
    this.clientInformation,
    this.tokens,
    this.codeVerifier,
    required this.createdAt,
    required this.updatedAt,
  });

  factory OAuthSessionModel.fromJson(Map<String, dynamic> json) =>
      _$OAuthSessionModelFromJson(json);
  Map<String, dynamic> toJson() => _$OAuthSessionModelToJson(this);

  factory OAuthSessionModel.fromDrift(OAuthSession drift) {
    return OAuthSessionModel(
      uuid: drift.uuid,
      mcpServerUuid: drift.mcpServerUuid,
      clientInformation: drift.clientInformation != null ? json.decode(drift.clientInformation!) : null,
      tokens: drift.tokens != null ? json.decode(drift.tokens!) : null,
      codeVerifier: drift.codeVerifier,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
    );
  }

  OAuthSessionsCompanion toCompanion() {
    return OAuthSessionsCompanion(
      uuid: Value(uuid),
      mcpServerUuid: Value(mcpServerUuid),
      clientInformation: Value(clientInformation != null ? json.encode(clientInformation) : null),
      tokens: Value(tokens != null ? json.encode(tokens) : null),
      codeVerifier: Value(codeVerifier),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }
} 