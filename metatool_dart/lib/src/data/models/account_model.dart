import 'package:drift/drift.dart' hide JsonKey;
import 'package:json_annotation/json_annotation.dart';
import '../database/app_database.dart';

part 'account_model.g.dart';

@JsonSerializable()
class AccountModel {
  final String id;
  final String accountId;
  final String providerId;
  final String userId;
  final String? accessToken;
  final String? refreshToken;
  final String? idToken;
  final DateTime? accessTokenExpiresAt;
  final DateTime? refreshTokenExpiresAt;
  final String? scope;
  final String? password;
  final DateTime createdAt;
  final DateTime updatedAt;

  AccountModel({
    required this.id,
    required this.accountId,
    required this.providerId,
    required this.userId,
    this.accessToken,
    this.refreshToken,
    this.idToken,
    this.accessTokenExpiresAt,
    this.refreshTokenExpiresAt,
    this.scope,
    this.password,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AccountModel.fromJson(Map<String, dynamic> json) => _$AccountModelFromJson(json);
  Map<String, dynamic> toJson() => _$AccountModelToJson(this);
  
  factory AccountModel.fromDrift(Account drift) {
    return AccountModel(
      id: drift.id,
      accountId: drift.accountId,
      providerId: drift.providerId,
      userId: drift.userId,
      accessToken: drift.accessToken,
      refreshToken: drift.refreshToken,
      idToken: drift.idToken,
      accessTokenExpiresAt: drift.accessTokenExpiresAt,
      refreshTokenExpiresAt: drift.refreshTokenExpiresAt,
      scope: drift.scope,
      password: drift.password,
      createdAt: drift.createdAt,
      updatedAt: drift.updatedAt,
    );
  }
  
  AccountsCompanion toCompanion() {
    return AccountsCompanion(
      id: Value(id),
      accountId: Value(accountId),
      providerId: Value(providerId),
      userId: Value(userId),
      accessToken: Value(accessToken),
      refreshToken: Value(refreshToken),
      idToken: Value(idToken),
      accessTokenExpiresAt: Value(accessTokenExpiresAt),
      refreshTokenExpiresAt: Value(refreshTokenExpiresAt),
      scope: Value(scope),
      password: Value(password),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }
} 