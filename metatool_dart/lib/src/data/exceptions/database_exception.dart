class DatabaseException implements Exception {
  final String message;
  final String? code;
  final dynamic cause;
  
  const DatabaseException(this.message, {this.code, this.cause});
  
  @override
  String toString() => 'DatabaseException: $message';
}

class ValidationException extends DatabaseException {
  ValidationException(String message) : super(message, code: 'VALIDATION_ERROR');
}

class UniqueConstraintException extends DatabaseException {
  final String field;
  UniqueConstraintException(String message, this.field) 
    : super(message, code: 'UNIQUE_CONSTRAINT');
}

class NotFoundException extends DatabaseException {
  NotFoundException(String message) : super(message, code: 'NOT_FOUND');
} 