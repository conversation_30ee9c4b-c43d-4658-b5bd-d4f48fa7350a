# mcp_dart 类关系图

## 概述

该文档使用 Mermaid 语法展示了 `mcp_dart` 库中核心类之间的关系，包括继承、组合和依赖。

## 类图

```mermaid
classDiagram
    class McpClient {
        +String serverUrl
        +Transport transport
        +connect() Future~void~
        +listTools() Future~List~Tool~~
        +callTool(String, Map) Future~dynamic~
    }

    class McpServer {
        +Transport transport
        +start() Future~void~
        +registerTool(Tool) void
        +registerResource(Resource) void
    }

    class Tool {
        +String name
        +String description
        +Map inputSchema
        +Map outputSchema
        +Function execute
    }

    class Resource {
        +String uri
        +String description
        +getContent() Future~dynamic~
    }

    class Transport {
        <<interface>>
        +connect()
        +send(data)
        +receive()
    }

    class StdioTransport {
        +String command
    }
    class StreamableHttpTransport {
        +String url
    }
    class IoStreamTransport {
        +Stream inStream
        +Stream outStream
    }

    McpServer *-- "1..*" Tool : 注册
    McpServer *-- "0..*" Resource : 注册
    McpClient ..> Tool : 调用
    McpClient ..> Resource : 访问

    McpClient --o Transport : 使用
    McpServer --o Transport : 使用

    Transport <|-- StdioTransport
    Transport <|-- StreamableHttpTransport
    Transport <|-- IoStreamTransport

```

### 图例说明

- **`--|>`**: 继承关系。例如，`StdioTransport` 继承自 `Transport`。
- **`*--`**: 组合关系。表示一个类包含另一个类的实例。例如，`McpServer` 包含多个 `Tool` 实例。
- **`..>`**: 依赖关系。表示一个类在操作中使用了另一个类。例如，`McpClient` 依赖 `Tool` 来执行调用。
- **`--o`**: 聚合关系。一种特殊的组合关系，表示部分可以独立于整体存在。