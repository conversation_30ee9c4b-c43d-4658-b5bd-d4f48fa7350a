# mcp_dart API 参考文档

## 概述

本文档详细介绍了 `mcp_dart` 库的公共 API。`mcp_dart` 提供了模型-上下文协议 (MCP) 的 Dart 实现，允许开发者构建可以与大型语言模型 (LLM) 无缝集成的客户端和服务器。

---

## `McpClient` 类

`McpClient` 用于连接到 MCP 服务器，发现可用的工具和资源，并执行它们。

### `McpClient.connect()`

连接到指定的 MCP 服务器。

- **方法签名**: `Future<void> connect(String serverUrl, {Transport? transport})`
- **功能描述**: 此方法通过指定的传输层（如果提供）与 MCP 服务器建立连接。如果未指定传输层，则会根据 `serverUrl` 的模式选择默认的传输机制。
- **参数说明**:
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| `serverUrl` | `String` | 是 | MCP 服务器的连接 URL。 |
| `transport` | `Transport?` | 否 | 用于通信的可选传输层实例。 |
- **使用示例**:
```dart
import 'package:mcp_dart/mcp_dart.dart';

void main() async {
  final client = McpClient();
  try {
    await client.connect('stdio://path/to/server/executable');
    print('成功连接到服务器！');
  } catch (e) {
    print('连接失败: $e');
  }
}
```

### `McpClient.listTools()`

列出服务器上所有可用的工具。

- **方法签名**: `Future<List<Tool>> listTools()`
- **功能描述**: 请求并返回 MCP 服务器上注册的所有工具的列表。每个 `Tool` 对象包含其名称、描述和输入/输出模式。
- **参数说明**: 无
- **使用示例**:
```dart
final tools = await client.listTools();
for (final tool in tools) {
  print('工具: ${tool.name}');
  print('  描述: ${tool.description}');
}
```

### `McpClient.callTool()`

调用服务器上的特定工具。

- **方法签名**: `Future<dynamic> callTool(String toolName, Map<String, dynamic> arguments)`
- **功能描述**: 使用提供的参数执行指定的工具。返回值是工具执行的结果，其类型取决于工具的输出模式。
- **参数说明**:
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| `toolName` | `String` | 是 | 要调用的工具的名称。 |
| `arguments` | `Map<String, dynamic>` | 是 | 提供给工具的参数。 |
- **错误代码**:
  - `404`: 未找到工具。
  - `400`: 无效的参数。
- **使用示例**:
```dart
try {
  final result = await client.callTool('calculator', {'expression': '2 + 2'});
  print('计算结果: $result'); // 输出: 4
} catch (e) {
  print('工具调用失败: $e');
}
```

---

## `McpServer` 类

`McpServer` 用于创建 MCP 服务器，托管工具和资源以供客户端访问。

### `McpServer.start()`

启动 MCP 服务器并开始监听客户端连接。

- **方法签名**: `Future<void> start({Transport? transport})`
- **功能描述**: 使用指定的传输层启动服务器。如果没有提供传输层，则会使用默认的 `StdioTransport`。
- **参数说明**:
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| `transport` | `Transport?` | 否 | 用于监听客户端连接的传输层实例。 |
- **使用示例**:
```dart
import 'package:mcp_dart/mcp_dart.dart';

void main() async {
  final server = McpServer();
  // ... 注册工具 ...
  await server.start();
  print('服务器已启动。');
}
```

### `McpServer.registerTool()`

在服务器上注册一个工具。

- **方法签名**: `void registerTool(Tool tool)`
- **功能描述**: 将一个 `Tool` 实例添加到服务器的工具注册表中，使其可供客户端发现和调用。
- **参数说明**:
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| `tool` | `Tool` | 是 | 要注册的 `Tool` 对象。 |
- **使用示例**:
```dart
final calculatorTool = Tool(
  name: 'calculator',
  description: '执行数学计算。',
  inputSchema: {'type': 'object', 'properties': {'expression': {'type': 'string'}}},
  outputSchema: {'type': 'number'},
  execute: (args) => evaluate(args['expression']),
);

server.registerTool(calculatorTool);
```

### `McpServer.registerResource()`

在服务器上注册一个资源。

- **方法签名**: `void registerResource(Resource resource)`
- **功能描述**: 将一个 `Resource` 实例添加到服务器的资源注册表中。资源可以是提示模板或外部数据源。
- **参数说明**:
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| `resource` | `Resource` | 是 | 要注册的 `Resource` 对象。 |
- **使用示例**:
```dart
final weatherResource = Resource(
  uri: 'weather://current',
  description: '获取当前天气数据。',
  // ... 其他资源属性 ...
);

server.registerResource(weatherResource);