# 架构概览

本文档详细描述了 `metamcp-backend` 项目的系统架构，包括其主要组件、它们之间的交互以及数据流动的方式。

## 1. 核心组件

系统主要由以下几个核心组件构成：

- **Express Web 服务器**: 作为应用的基础，负责处理 HTTP 请求和路由。
- **tRPC API 网关**: 提供用于前端管理的类型安全的 API。
- **MCP 代理**: 核心功能，负责在客户端和后端的 MCP 服务器之间双向代理通信。
- **身份验证服务 (`better-auth`)**: 处理用户登录、会话管理和 API 访问控制。
- **数据库 (PostgreSQL + Drizzle ORM)**: 用于持久化存储应用配置、用户数据、API 密钥等。

## 2. 架构图

下图使用 Mermaid.js 展示了系统的整体架构和组件之间的交互流程。

```mermaid
graph TD
    subgraph "客户端 (Browser/CLI)"
        A[前端应用] -->|tRPC 请求 (HTTPS)| B
        C[MCP 客户端] -->|WebSocket/HTTP| D{MCP 代理}
    end

    subgraph "MetaMCP 后端 (本应用)"
        B(Express 服务器)
        subgraph "路由"
            B --> R1[/trpc]
            B --> R2[/mcp-proxy]
            B --> R3[/api/auth]
            B --> R4[/metamcp]
        end

        R1 --> T(tRPC 网关)
        R2 --> D
        R3 --> AUTH(身份验证服务)

        T -->|数据库操作| DB[(数据库)]
        AUTH -->|会话/用户查询| DB

        subgraph "核心代理逻辑"
            D --> P(mcp-proxy.ts)
        end
    end

    subgraph "外部服务"
        P -->|代理请求| E[后端 MCP 服务器]
    end

    style A fill:#cde4ff
    style C fill:#cde4ff
    style E fill:#d5e8d4
```

## 3. 组件交互说明

### 3.1. 前端管理界面 (tRPC)

1.  **请求流程**:
    - 前端应用（通常是浏览器中的管理后台）向 `/trpc/frontend/...` 发起 HTTPS 请求。
    - Express 服务器接收请求并将其路由到 **tRPC 网关**。
2.  **身份验证**:
    - tRPC 的 `createContext` 函数会检查请求中的 `cookie`。
    - 它会调用**身份验证服务** (`/api/auth/get-session`) 来验证会话并获取用户信息。
    - 如果是受保护的路由，并且用户未通过身份验证，则请求将被拒绝。
3.  **业务逻辑**:
    - tRPC 的 `implementations`（例如 `mcp-servers.impl.ts`）处理请求的业务逻辑。
    - 这些实现通过 **Drizzle ORM** 与**数据库**交互，执行 CRUD 操作（例如，创建/读取/更新/删除 MCP 服务器配置）。
4.  **响应**:
    - tRPC 将结果序列化为 JSON 并通过 Express 返回给前端。

### 3.2. MCP 代理

1.  **连接建立**:
    - MCP 客户端（例如，一个开发工具或 CLI）向 `/mcp-proxy` 端点发起连接（可能是 WebSocket 或其他流式协议）。
    - Express 将请求路由到 **MCP 代理**模块。
2.  **双向通信**:
    - `mcp-proxy.ts` 中的逻辑被激活。它建立了两个 `Transport` 对象：一个连接到客户端，另一个连接到目标**后端 MCP 服务器**。
    - 代理在这两个传输通道之间双向转发消息，不关心消息的具体内容。
3.  **错误处理与连接管理**:
    - 代理负责处理其中任一端连接关闭或发生错误的情况，并确保另一端也相应地关闭连接。

### 3.3. 身份验证服务

1.  **请求处理**:
    - 所有对 `/api/auth/...` 的请求（例如登录、登出）都被直接路由到 `better-auth` 库的处理程序。
    - 该服务独立处理身份验证逻辑，如处理 OAuth 回调或基于凭据的登录。
2.  **与 tRPC 集成**:
    - 如上所述，tRPC 上下文利用此服务来验证用户会话，从而保护其 API 端点。

## 4. 总结

该后端应用是一个功能强大的网关和代理服务器。它通过 tRPC 提供了一个安全的管理接口，同时其核心的 MCP 代理功能实现了客户端与任意 MCP 服务器之间的解耦和通信中继。这种架构具有良好的模块化和可扩展性。