# TypeScript 到 Dart 项目移植计划

本文档详细说明了将现有 TypeScript 后端项目移植到新的 Dart 项目的完整计划。

## 1. TypeScript 项目分析

经过对 `package.json`, `src/index.ts`, `src/trpc.ts`, `src/db/schema.ts` 和 `src/auth.ts` 等核心文件的分析，我们总结出该 TypeScript 项目的核心架构和技术栈如下：

*   **Web 框架**: 使用 **Express.js** 作为基础的 Web 服务器。
*   **API 层**: 采用 **tRPC** 构建类型安全的 API，这是业务逻辑暴露给前端的主要方式。
*   **数据库**: 使用 **PostgreSQL** 数据库，并通过 **Drizzle ORM** 进行数据操作。数据库 Schema 定义了应用的核心数据模型，如 `mcp_servers`, `users`, `sessions`, `namespaces` 等。
*   **认证**: 依赖 **better-auth** 库处理用户认证和会话管理，它通过一个 Drizzle 适配器与数据库紧密集成。
*   **MCP 功能**: 通过 `@modelcontextprotocol/sdk` 库实现与模型上下文协议（MCP）相关的功能。
*   **模块化结构**: 项目结构清晰，按功能将代码分离到 `db`, `routers`, `trpc`, `lib`, `middleware` 等目录中。

## 2. Dart 项目架构设计

为了将现有功能平稳地移植到 Dart，并遵循 Dart 的最佳实践，我们设计了以下目录结构，该结构将被创建在 `metatools` 目录下：

```
metatools/
├── bin/
│   └── server.dart           # 应用入口，负责启动服务器和初始化服务
├── lib/
│   ├── src/
│   │   ├── api/                # API 层：路由、中间件和控制器
│   │   │   ├── controllers/      # 处理HTTP请求，调用服务层
│   │   │   │   ├── mcp_proxy_controller.dart
│   │   │   │   └── trpc_controller.dart
│   │   │   ├── middleware/       # 请求处理中间件
│   │   │   │   └── auth_middleware.dart
│   │   │   └── routers/          # 定义API路由
│   │   │       ├── api_router.dart
│   │   │       └── mcp_proxy_router.dart
│   │   │
│   │   ├── core/               # 核心服务和通用工具
│   │   │   ├── config/           # 应用配置管理
│   │   │   │   └── config_service.dart
│   │   │   └── mcp/              # MCP客户端和代理逻辑
│   │   │       ├── mcp_client.dart
│   │   │       └── mcp_proxy.dart
│   │   │
│   │   ├── data/               # 数据持久化层
│   │   │   ├── db.dart           # 数据库连接初始化 (使用 sqlite_dart)
│   │   │   ├── models/           # 数据模型类 (对应原 schema.ts)
│   │   │   │   ├── mcp_server.dart
│   │   │   │   ├── user.dart
│   │   │   │   └── ...
│   │   │   ├── repositories/     # 数据仓库，封装数据访问逻辑
│   │   │   │   ├── base_repository.dart
│   │   │   │   └── mcp_servers_repository.dart
│   │   │   │   └── ...
│   │   │   └── serializers/      # 数据序列化/反序列化
│   │   │       ├── mcp_server_serializer.dart
│   │   │       └── ...
│   │   │
│   │   └── services/           # 业务逻辑层
│   │       ├── auth_service.dart   # 认证和授权服务
│   │       ├── mcp_service.dart    # MCP相关业务逻辑
│   │       └── ...
│   │
│   └── metatools.dart        # 库的公共导出文件
│
├── pubspec.yaml              # Dart项目依赖管理文件
├── analysis_options.yaml     # 代码风格与静态分析配置文件
└── README.md                 # 项目说明文档
```

### 架构图 (Mermaid)

```mermaid
graph TD
    subgraph "Entry Point (bin)"
        A[bin/server.dart]
    end

    subgraph "API Layer (lib/src/api)"
        B[Routers]
        C[Middleware]
        D[Controllers]
    end

    subgraph "Service Layer (lib/src/services)"
        E[Business Services e.g., AuthService, McpService]
    end

    subgraph "Core Layer (lib/src/core)"
        F[Config Service]
        G[MCP Client/Proxy]
    end

    subgraph "Data Layer (lib/src/data)"
        H[Repositories]
        I[Models]
        J[Serializers]
        K[Database (sqlite_dart)]
    end

    A --> B
    B -- Uses --> C
    B -- Routes to --> D
    D -- Calls --> E
    E -- Uses --> F
    E -- Uses --> G
    E -- Calls --> H
    H -- Accesses --> I
    H -- Uses --> K
    D -- Uses --> J
    J -- Transforms --> I

    style K fill:#c9f,stroke:#333,stroke-width:2px
```

## 3. 详细移植计划

我们将移植过程分为以下几个有序的阶段，以确保平稳过渡和风险控制。

### 阶段 1: 项目基础搭建

1.  **创建 Dart 项目**: 在 `metatools` 目录下初始化一个新的 Dart 服务器项目。
2.  **添加依赖**: 在 `pubspec.yaml` 中声明所有必需的库，包括 `sqlite_dart: ^any`, `mcp_dart: ^0.5.2`, `shelf`, `shelf_router`, `http`, `dotenv`, 和 `lints`。
3.  **建立目录结构**: 根据上述设计的架构创建所有目录和空的 `.dart` 文件，为后续代码填充做好准备。

### 阶段 2: 数据层移植

1.  **定义数据模型**: 将 TypeScript 的 Drizzle Schema (`src/db/schema.ts`) 逐个翻译成 Dart 类，存放在 `lib/src/data/models/` 目录下。这将是新项目数据结构的基础。
2.  **实现数据库交互**: 在 `lib/src/data/db.dart` 中设置 `sqlite_dart` 的数据库连接。编写数据库表创建的逻辑。
3.  **实现 Repositories**: 将 TypeScript 项目中 `src/db/repositories/` 的数据查询逻辑，使用 `sqlite_dart` 的 API 在 Dart 的 Repositories 中重新实现。这将把数据访问逻辑与业务逻辑解耦。
4.  **实现 Serializers**: 创建数据序列化器，用于将 Dart 的数据模型对象转换为 JSON 格式，供 API 返回。

### 阶段 3: 核心服务与认证移植

1.  **配置服务**: 移植 `ConfigService`，用于管理应用级别的配置项。
2.  **重建认证服务**: 这是关键一步。由于 `better-auth` 是一个 JavaScript 库，我们需要在 Dart 中重新实现其核心功能。这包括用户注册、登录、会话令牌管理、密码安全存储等。可以利用 Dart 社区的 JWT 库来简化令牌处理。
3.  **移植 MCP 逻辑**: 使用 `mcp_dart` 库，将 `src/lib/metamcp/` 目录下的 MCP 客户端和代理逻辑移植到 `lib/src/core/mcp/`。

### 阶段 4: API 层移植

1.  **实现中间件**: 创建 `auth_middleware`，用于保护需要认证的路由。它将从请求中解析会话令牌，并使用 `AuthService` 进行验证。
2.  **实现控制器**: 将 tRPC 实现 (`*.impl.ts`) 中的业务逻辑，移植到 `lib/src/api/controllers/` 下的 Dart 类中。
3.  **构建路由**: 使用 `shelf_router` 在 `lib/src/api/routers/` 中定义所有 API 端点，并将它们映射到对应的控制器方法。
4.  **启动服务器**: 在 `bin/server.dart` 中，完成所有服务的初始化，并启动 Shelf HTTP 服务器。

### 阶段 5: 测试、优化与文档

1.  **编写测试**: 为关键的业务逻辑、数据仓库和 API 端点编写单元测试和集成测试。
2.  **代码审查与重构**: 对所有移植的代码进行审查，确保其符合 Dart 的编程范式和最佳实践。
3.  **完善文档**: 更新 `README.md`，提供详细的项目设置、运行和部署指南。