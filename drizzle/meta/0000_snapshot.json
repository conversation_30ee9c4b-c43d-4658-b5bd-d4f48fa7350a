{"id": "f3c5452b-2b28-4b96-9d2c-c4da571f728a", "prevId": "********-0000-0000-0000-************", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"api_keys_user_id_idx": {"name": "api_keys_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_keys_key_idx": {"name": "api_keys_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "api_keys_is_active_idx": {"name": "api_keys_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_key_unique": {"name": "api_keys_key_unique", "nullsNotDistinct": false, "columns": ["key"]}, "api_keys_name_per_user_idx": {"name": "api_keys_name_per_user_idx", "nullsNotDistinct": false, "columns": ["user_id", "name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.config": {"name": "config", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.endpoints": {"name": "endpoints", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "namespace_uuid": {"name": "namespace_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "enable_api_key_auth": {"name": "enable_api_key_auth", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "use_query_param_auth": {"name": "use_query_param_auth", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"endpoints_namespace_uuid_idx": {"name": "endpoints_namespace_uuid_idx", "columns": [{"expression": "namespace_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"endpoints_namespace_uuid_namespaces_uuid_fk": {"name": "endpoints_namespace_uuid_namespaces_uuid_fk", "tableFrom": "endpoints", "tableTo": "namespaces", "columnsFrom": ["namespace_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"endpoints_name_unique": {"name": "endpoints_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "endpoints_name_unique_idx": {"name": "endpoints_name_unique_idx", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mcp_servers": {"name": "mcp_servers", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "mcp_server_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'STDIO'"}, "command": {"name": "command", "type": "text", "primaryKey": false, "notNull": false}, "args": {"name": "args", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "env": {"name": "env", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "bearer_token": {"name": "bearer_token", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"mcp_servers_type_idx": {"name": "mcp_servers_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"mcp_servers_name_unique_idx": {"name": "mcp_servers_name_unique_idx", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.namespace_server_mappings": {"name": "namespace_server_mappings", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "namespace_uuid": {"name": "namespace_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "mcp_server_uuid": {"name": "mcp_server_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "mcp_server_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"namespace_server_mappings_namespace_uuid_idx": {"name": "namespace_server_mappings_namespace_uuid_idx", "columns": [{"expression": "namespace_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "namespace_server_mappings_mcp_server_uuid_idx": {"name": "namespace_server_mappings_mcp_server_uuid_idx", "columns": [{"expression": "mcp_server_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "namespace_server_mappings_status_idx": {"name": "namespace_server_mappings_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"namespace_server_mappings_namespace_uuid_namespaces_uuid_fk": {"name": "namespace_server_mappings_namespace_uuid_namespaces_uuid_fk", "tableFrom": "namespace_server_mappings", "tableTo": "namespaces", "columnsFrom": ["namespace_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}, "namespace_server_mappings_mcp_server_uuid_mcp_servers_uuid_fk": {"name": "namespace_server_mappings_mcp_server_uuid_mcp_servers_uuid_fk", "tableFrom": "namespace_server_mappings", "tableTo": "mcp_servers", "columnsFrom": ["mcp_server_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"namespace_server_mappings_unique_idx": {"name": "namespace_server_mappings_unique_idx", "nullsNotDistinct": false, "columns": ["namespace_uuid", "mcp_server_uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.namespace_tool_mappings": {"name": "namespace_tool_mappings", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "namespace_uuid": {"name": "namespace_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "tool_uuid": {"name": "tool_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "mcp_server_uuid": {"name": "mcp_server_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "mcp_server_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"namespace_tool_mappings_namespace_uuid_idx": {"name": "namespace_tool_mappings_namespace_uuid_idx", "columns": [{"expression": "namespace_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "namespace_tool_mappings_tool_uuid_idx": {"name": "namespace_tool_mappings_tool_uuid_idx", "columns": [{"expression": "tool_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "namespace_tool_mappings_mcp_server_uuid_idx": {"name": "namespace_tool_mappings_mcp_server_uuid_idx", "columns": [{"expression": "mcp_server_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "namespace_tool_mappings_status_idx": {"name": "namespace_tool_mappings_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"namespace_tool_mappings_namespace_uuid_namespaces_uuid_fk": {"name": "namespace_tool_mappings_namespace_uuid_namespaces_uuid_fk", "tableFrom": "namespace_tool_mappings", "tableTo": "namespaces", "columnsFrom": ["namespace_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}, "namespace_tool_mappings_tool_uuid_tools_uuid_fk": {"name": "namespace_tool_mappings_tool_uuid_tools_uuid_fk", "tableFrom": "namespace_tool_mappings", "tableTo": "tools", "columnsFrom": ["tool_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}, "namespace_tool_mappings_mcp_server_uuid_mcp_servers_uuid_fk": {"name": "namespace_tool_mappings_mcp_server_uuid_mcp_servers_uuid_fk", "tableFrom": "namespace_tool_mappings", "tableTo": "mcp_servers", "columnsFrom": ["mcp_server_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"namespace_tool_mappings_unique_idx": {"name": "namespace_tool_mappings_unique_idx", "nullsNotDistinct": false, "columns": ["namespace_uuid", "tool_uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.namespaces": {"name": "namespaces", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"namespaces_name_unique": {"name": "namespaces_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.oauth_sessions": {"name": "oauth_sessions", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "mcp_server_uuid": {"name": "mcp_server_uuid", "type": "uuid", "primaryKey": false, "notNull": true}, "client_information": {"name": "client_information", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "tokens": {"name": "tokens", "type": "jsonb", "primaryKey": false, "notNull": false}, "code_verifier": {"name": "code_verifier", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"oauth_sessions_mcp_server_uuid_idx": {"name": "oauth_sessions_mcp_server_uuid_idx", "columns": [{"expression": "mcp_server_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"oauth_sessions_mcp_server_uuid_mcp_servers_uuid_fk": {"name": "oauth_sessions_mcp_server_uuid_mcp_servers_uuid_fk", "tableFrom": "oauth_sessions", "tableTo": "mcp_servers", "columnsFrom": ["mcp_server_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"oauth_sessions_unique_per_server_idx": {"name": "oauth_sessions_unique_per_server_idx", "nullsNotDistinct": false, "columns": ["mcp_server_uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tools": {"name": "tools", "schema": "", "columns": {"uuid": {"name": "uuid", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "tool_schema": {"name": "tool_schema", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "mcp_server_uuid": {"name": "mcp_server_uuid", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"tools_mcp_server_uuid_idx": {"name": "tools_mcp_server_uuid_idx", "columns": [{"expression": "mcp_server_uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tools_mcp_server_uuid_mcp_servers_uuid_fk": {"name": "tools_mcp_server_uuid_mcp_servers_uuid_fk", "tableFrom": "tools", "tableTo": "mcp_servers", "columnsFrom": ["mcp_server_uuid"], "columnsTo": ["uuid"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tools_unique_tool_name_per_server_idx": {"name": "tools_unique_tool_name_per_server_idx", "nullsNotDistinct": false, "columns": ["mcp_server_uuid", "name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verifications": {"name": "verifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.mcp_server_status": {"name": "mcp_server_status", "schema": "public", "values": ["ACTIVE", "INACTIVE"]}, "public.mcp_server_type": {"name": "mcp_server_type", "schema": "public", "values": ["STDIO", "SSE", "STREAMABLE_HTTP"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}