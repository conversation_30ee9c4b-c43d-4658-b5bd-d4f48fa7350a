# MCP Dart API 参考手册

> Model Context Protocol (MCP) Dart 实现的完整API参考手册

**版本**: 0.5.2  
**协议版本**: 2025-03-26 (兼容 2024-11-05, 2024-10-07)  
**Dart SDK**: ^3.0.0  

---

## 📖 目录

1. [项目概述](#项目概述)
2. [安装配置](#安装配置)
3. [快速开始](#快速开始)
4. [核心概念](#核心概念)
5. [客户端API](#客户端api)
6. [服务器API](#服务器api)
7. [传输层](#传输层)
8. [数据类型](#数据类型)
9. [示例代码](#示例代码)
10. [最佳实践](#最佳实践)
11. [常见问题](#常见问题)

---

## 项目概述

### 🎯 什么是 MCP Dart？

MCP Dart 是 [Model Context Protocol](https://modelcontextprotocol.io/) 的Dart官方实现，提供了一个简单直观的方式来实现MCP服务器和客户端。MCP是一个开放协议，旨在实现LLM应用与外部数据源和工具之间的无缝集成。

### ✨ 核心特性

- **🔌 多种传输方式**: 支持Stdio、StreamableHTTP、SSE、IOStream四种传输协议
- **🛠️ 完整功能支持**: 工具(Tools)、资源(Resources)、提示(Prompts)、采样(Sampling)、根目录(Roots)
- **🏗️ 清晰架构**: 四层设计 - 共享基础、传输实现、协议处理、高级API
- **💪 强类型安全**: 2700+行完整类型定义，确保编译时安全
- **🔄 协议兼容**: 100%兼容MCP规范，向后兼容多个版本
- **🚀 高性能**: 异步设计，支持并发和流式处理

### 🏛️ 项目架构

```
mcp_dart/
├── lib/
│   ├── mcp_dart.dart          # 主导出文件
│   └── src/
│       ├── client/            # 客户端模块 (4文件, 1.4万行)
│       │   ├── client.dart    # 核心客户端类
│       │   ├── stdio.dart     # 标准IO客户端传输
│       │   └── streamable_https.dart # HTTP客户端传输
│       ├── server/            # 服务器模块 (7文件, 4.7万行)
│       │   ├── mcp.dart       # 高级服务器API
│       │   ├── server.dart    # 基础服务器实现
│       │   ├── stdio.dart     # 标准IO服务器传输
│       │   ├── sse.dart       # SSE服务器传输
│       │   └── streamable_https.dart # HTTP服务器传输
│       ├── shared/            # 共享模块 (7文件, 4.8万行)
│       │   ├── protocol.dart  # 核心协议处理引擎
│       │   ├── transport.dart # 传输层抽象接口
│       │   ├── iostream.dart  # 流传输实现
│       │   ├── stdio.dart     # 标准IO工具
│       │   ├── uri_template.dart # URI模板处理
│       │   └── uuid.dart      # UUID生成器
│       └── types.dart         # 类型定义 (2712行)
└── example/                   # 示例代码
    ├── server_stdio.dart      # 基础服务器示例
    ├── client_stdio.dart      # 基础客户端示例
    ├── weather.dart           # 天气服务示例
    └── anthropic-client/      # Anthropic集成示例
```

### 🎭 核心设计理念

1. **简单易用**: 提供高级API(`McpServer`)和底层API(`Server`)，适配不同使用场景
2. **类型安全**: 完整的Dart类型系统，编译时错误检查
3. **插拔架构**: 传输层可插拔，支持多种通信方式
4. **标准兼容**: 严格遵循MCP协议规范，确保互操作性
5. **扩展友好**: 抽象接口设计，便于添加新功能和传输方式

### 🌟 与其他语言SDK的对比

| 特性 | MCP Dart | TypeScript SDK | Python SDK |
|------|----------|----------------|------------|
| 类型安全 | ✅ 强类型 | ✅ 强类型 | ⚡ 运行时检查 |
| 性能 | ✅ 高性能 | ✅ 高性能 | ✅ 中等性能 |
| 异步支持 | ✅ 原生async/await | ✅ Promise/async | ✅ asyncio |
| 传输方式 | 4种 | 4种 | 3种 |
| 生态系统 | Flutter/Dart | Node.js/Web | 丰富生态 |
| 学习曲线 | 中等 | 中等 | 简单 |

---

## 安装配置

### 📦 添加依赖

在您的 `pubspec.yaml` 文件中添加依赖：

```yaml
dependencies:
  mcp_dart: ^0.5.2
```

然后运行：

```bash
dart pub get
```

### 🔧 环境要求

- **Dart SDK**: 3.0.0 或更高版本
- **平台支持**: 
  - ✅ Windows (Stdio, HTTP)
  - ✅ macOS (Stdio, HTTP) 
  - ✅ Linux (Stdio, HTTP)
  - ✅ Web (仅HTTP传输)
  - ✅ Mobile (Flutter应用中的HTTP传输)

### 📥 导入库

```dart
import 'package:mcp_dart/mcp_dart.dart';
```

这将导入所有核心API：

- **客户端**: `Client`, `StdioClientTransport`, `StreamableHttpClientTransport`
- **服务器**: `McpServer`, `Server`, `StdioServerTransport`, `SseServerTransport` 
- **传输**: `Transport`, `IOStreamTransport`
- **类型**: 所有MCP协议数据类型
- **工具**: `generateUUID`

### ⚡ 验证安装

创建一个简单的测试文件验证安装：

```dart
import 'package:mcp_dart/mcp_dart.dart';

void main() {
  print('MCP Dart 版本检查');
  print('支持的协议版本: $supportedProtocolVersions');
  print('最新协议版本: $latestProtocolVersion');
  print('JSON-RPC版本: $jsonRpcVersion');
  
  // 创建一个简单的服务器实例
  final server = McpServer(
    Implementation(name: "test-server", version: "1.0.0"),
  );
  
  print('✅ MCP Dart 安装成功！');
}
```

运行测试：

```bash
dart run test_mcp.dart
```

预期输出：
```
MCP Dart 版本检查
支持的协议版本: [2025-03-26, 2024-11-05, 2024-10-07]
最新协议版本: 2025-03-26
JSON-RPC版本: 2.0
✅ MCP Dart 安装成功！
```

---

## 快速开始

### 🚀 5分钟搭建第一个MCP服务器

```dart
import 'package:mcp_dart/mcp_dart.dart';

void main() async {
  // 1. 创建服务器
  final server = McpServer(
    Implementation(name: "my-first-server", version: "1.0.0"),
    options: ServerOptions(
      capabilities: ServerCapabilities(
        tools: ServerCapabilitiesTools(),
      ),
    ),
  );

  // 2. 注册一个简单工具
  server.tool(
    'hello',
    description: '友好的问候工具',
    inputSchemaProperties: {
      'name': {'type': 'string', 'description': '要问候的姓名'},
    },
    callback: ({args, extra}) async {
      final name = args?['name'] ?? 'World';
      return CallToolResult.fromContent(
        content: [TextContent(text: '你好, $name! 👋')],
      );
    },
  );

  // 3. 启动服务器 (Stdio传输)
  await server.connect(StdioServerTransport());
  print('🎉 MCP服务器已启动！');
}
```

### 🔗 连接到服务器的客户端

```dart
import 'package:mcp_dart/mcp_dart.dart';

void main() async {
  // 1. 配置服务器连接参数
  final transport = StdioClientTransport(
    StdioServerParameters(
      command: 'dart',
      args: ['run', 'my_server.dart'],
    ),
  );

  // 2. 创建客户端
  final client = Client(Implementation(name: "my-client", version: "1.0.0"));

  try {
    // 3. 连接到服务器
    await client.connect(transport);
    print('✅ 已连接到服务器');

    // 4. 列出可用工具
    final tools = await client.listTools();
    print('可用工具: ${tools.tools.map((t) => t.name).join(', ')}');

    // 5. 调用工具
    final result = await client.callTool(
      CallToolRequestParams(
        name: 'hello',
        arguments: {'name': '张三'},
      ),
    );
    print('工具结果: ${result.content.first}');

  } finally {
    // 6. 清理资源
    await client.close();
  }
}
```

### 📖 更多示例

查看 `example/` 目录获取更多完整示例：

- **天气服务**: 实际的HTTP API集成示例
- **Anthropic集成**: 与Claude API的集成示例  
- **资源管理**: 文件系统资源访问示例
- **HTTP传输**: 使用StreamableHTTP的Web应用示例

---

## 核心概念

### 🔄 MCP协议基础

#### 什么是Model Context Protocol？

Model Context Protocol (MCP) 是一个开放标准，用于LLM应用与外部数据源和工具之间的安全、受控集成。它解决了LLM应用连接外部系统时的复杂性和安全性问题。

#### 协议特点

- **📜 标准化**: 基于JSON-RPC 2.0，确保互操作性
- **🔒 安全性**: 内置权限控制和能力声明机制
- **⚡ 实时性**: 支持双向通信和实时更新
- **🔌 可扩展**: 模块化设计，支持自定义扩展

#### 架构模型

```
┌─────────────┐         ┌─────────────┐         ┌─────────────┐
│    LLM      │◄────────┤   Client    │◄────────┤   Server    │
│ Application │         │             │         │             │
│             │         │             │         │             │
│ (Claude,    │         │ (MCP Client)│         │ (Your App)  │
│  GPT, etc.) │         │             │         │             │
└─────────────┘         └─────────────┘         └─────────────┘
```

### 🏗️ 四层架构设计

MCP Dart采用清晰的四层架构：

#### 1. 高级API层
- **目的**: 简化开发，提供开箱即用的功能
- **组件**: `McpServer` (高级服务器), `Client` (功能完整客户端)
- **特点**: 自动处理复杂逻辑，类型安全，错误处理

```dart
// 高级API - 简单易用
final server = McpServer(Implementation(name: "my-server", version: "1.0.0"));
server.tool('calculate', callback: myCallback);
```

#### 2. 协议处理层
- **目的**: 实现MCP协议逻辑和JSON-RPC消息流
- **组件**: `Protocol` (抽象基类), `Server` (基础服务器), 消息路由
- **特点**: 协议标准化，请求/响应管理，错误处理

```dart
// 协议层 - 更多控制权
final server = Server(Implementation(name: "my-server", version: "1.0.0"));
server.setRequestHandler<JsonRpcCallToolRequest>('tools/call', myHandler);
```

#### 3. 传输实现层
- **目的**: 提供多种通信方式的具体实现
- **组件**: `StdioTransport`, `StreamableHttpTransport`, `SseTransport`, `IOStreamTransport`
- **特点**: 插拔式设计，协议无关

#### 4. 共享基础层
- **目的**: 提供公共工具和抽象接口
- **组件**: `Transport` (接口), 数据类型, 工具函数
- **特点**: 高度复用，类型安全

### 🚀 传输方式详解

#### 1. Stdio Transport (标准输入输出)
**适用场景**: 命令行工具、子进程通信

```dart
// 服务器端
await server.connect(StdioServerTransport());

// 客户端
final transport = StdioClientTransport(
  StdioServerParameters(command: 'dart', args: ['run', 'server.dart']),
);
await client.connect(transport);
```

**特点**:
- ✅ 简单可靠，跨平台支持
- ✅ 进程隔离，安全性高
- ⚠️ 仅限本地通信
- ⚠️ 需要管理子进程生命周期

#### 2. StreamableHTTP Transport (流式HTTP)
**适用场景**: Web应用、远程服务、云部署

```dart
// 服务器端
final httpTransport = StreamableHTTPServerTransport(
  options: StreamableHTTPServerTransportOptions(
    sessionIdGenerator: () => generateUUID(),
  ),
);

// 客户端
final clientTransport = StreamableHttpClientTransport(
  Uri.parse('https://api.example.com/mcp'),
);
```

**特点**:
- ✅ 支持远程通信
- ✅ 会话管理和认证
- ✅ 可恢复性支持
- ✅ 防火墙友好

#### 3. SSE Transport (Server-Sent Events)
**适用场景**: 实时Web应用、浏览器客户端

```dart
// 服务器端
final sseTransport = SseServerTransport(
  response: httpResponse,
  messageEndpointPath: '/messages',
);

// 管理器
final manager = SseServerManager(server);
await manager.handleRequest(httpRequest);
```

**特点**:
- ✅ 实时双向通信
- ✅ Web标准，浏览器原生支持
- ⚠️ 需要HTTP服务器基础设施

#### 4. IOStream Transport (内存流)
**适用场景**: 测试、嵌入式应用、同进程通信

```dart
final controller = StreamController<List<int>>();
final transport = IOStreamTransport(
  stream: controller.stream,
  sink: controller.sink,
);
```

**特点**:
- ✅ 高性能，零延迟
- ✅ 完全控制数据流
- ✅ 测试友好
- ⚠️ 仅限同进程

### 📨 消息格式和流程

#### JSON-RPC 2.0基础

所有MCP消息都基于JSON-RPC 2.0标准：

```json
{
  "jsonrpc": "2.0",
  "id": 123,
  "method": "tools/call",
  "params": {
    "name": "calculator",
    "arguments": {"a": 1, "b": 2}
  }
}
```

#### 消息类型

1. **请求 (Request)**
```dart
class JsonRpcRequest extends JsonRpcMessage {
  final RequestId id;        // 请求标识符
  final String method;       // 方法名
  final Map<String, dynamic>? params;  // 参数
}
```

2. **响应 (Response)**
```dart
class JsonRpcResponse extends JsonRpcMessage {
  final RequestId id;        // 对应请求ID
  final Map<String, dynamic> result;   // 结果数据
}
```

3. **通知 (Notification)**
```dart
class JsonRpcNotification extends JsonRpcMessage {
  final String method;       // 方法名
  final Map<String, dynamic>? params;  // 参数
  // 注意：通知没有id，不期待响应
}
```

4. **错误 (Error)**
```dart
class JsonRpcError extends JsonRpcMessage {
  final RequestId id;        // 对应请求ID  
  final JsonRpcErrorData error;         // 错误信息
}
```

#### 初始化流程

MCP连接的建立遵循标准握手过程：

```dart
// 1. 客户端发送初始化请求
final initRequest = JsonRpcInitializeRequest(
  id: 1,
  initParams: InitializeRequestParams(
    protocolVersion: latestProtocolVersion,
    capabilities: ClientCapabilities(tools: {}, resources: {}),
    clientInfo: Implementation(name: "my-client", version: "1.0.0"),
  ),
);

// 2. 服务器响应能力和信息
final initResult = InitializeResult(
  protocolVersion: latestProtocolVersion,
  capabilities: ServerCapabilities(tools: ServerCapabilitiesTools()),
  serverInfo: Implementation(name: "my-server", version: "1.0.0"),
);

// 3. 客户端确认初始化完成
const initializedNotif = JsonRpcInitializedNotification();
```

### 🎛️ 能力系统 (Capabilities)

#### 能力声明的作用

能力系统确保客户端和服务器之间的功能匹配，避免不支持的操作：

```dart
// 客户端能力
class ClientCapabilities {
  final Map<String, dynamic>? sampling;    // 支持LLM采样
  final ClientCapabilitiesRoots? roots;    // 支持根目录操作
}

// 服务器能力
class ServerCapabilities {
  final ServerCapabilitiesTools? tools;        // 提供工具
  final ServerCapabilitiesResources? resources; // 提供资源
  final ServerCapabilitiesPrompts? prompts;    // 提供提示
  final ServerCapabilitiesLogging? logging;    // 提供日志
}
```

#### 能力检查机制

在发送请求前，会自动检查对方是否支持相应功能：

```dart
// 自动能力检查
@override
void assertCapabilityForMethod(String method) {
  switch (method) {
    case "tools/call":
      if (serverCapabilities?.tools == null) {
        throw McpError(ErrorCode.invalidRequest.value, 
          "Server does not support tools");
      }
      break;
    // ... 其他检查
  }
}
```

### 🔧 五大功能模块

#### 1. 工具系统 (Tools)
允许LLM调用外部功能：

```dart
// 注册工具
server.tool(
  'web_search',
  description: '在网上搜索信息',
  inputSchemaProperties: {
    'query': {'type': 'string', 'description': '搜索关键词'},
    'limit': {'type': 'integer', 'default': 10},
  },
  callback: ({args, extra}) async {
    final query = args!['query'] as String;
    final results = await searchEngine.search(query);
    return CallToolResult.fromContent(
      content: [TextContent(text: results.join('\n'))],
    );
  },
);
```

#### 2. 资源系统 (Resources)
提供结构化数据访问：

```dart
// 静态资源
server.resource(
  'database_schema',
  'file:///schema.sql',
  (uri, extra) async {
    final content = await File.fromUri(uri).readAsString();
    return ReadResourceResult(
      contents: [TextResourceContents(uri: uri.toString(), text: content)],
    );
  },
);

// 动态资源模板
server.resourceTemplate(
  'user_profile',
  ResourceTemplateRegistration('user://profile/{userId}'),
  (uri, variables, extra) async {
    final userId = variables['userId'];
    final profile = await database.getUserProfile(userId);
    return ReadResourceResult(
      contents: [TextResourceContents(
        uri: uri.toString(),
        text: jsonEncode(profile),
        mimeType: 'application/json',
      )],
    );
  },
);
```

#### 3. 提示系统 (Prompts)
生成上下文相关的提示：

```dart
server.prompt(
  'code_review',
  description: '代码审查提示生成器',
  argsSchema: {
    'language': PromptArgumentDefinition(
      type: String,
      description: '编程语言',
      required: true,
    ),
    'complexity': PromptArgumentDefinition(
      type: String,
      description: '复杂度级别',
      required: false,
    ),
  },
  callback: (args, extra) async {
    final language = args['language'];
    final complexity = args['complexity'] ?? 'medium';
    
    return GetPromptResult(
      messages: [
        PromptMessage(
          role: PromptMessageRole.user,
          content: TextContent(
            text: '请审查以下$language代码，复杂度级别：$complexity...',
          ),
        ),
      ],
    );
  },
);
```

#### 4. 采样系统 (Sampling)
与LLM进行交互：

```dart
// 服务器可以向客户端请求LLM采样
final samplingRequest = JsonRpcCreateMessageRequest(
  id: 1,
  createParams: CreateMessageRequestParams(
    messages: [
      SamplingMessage(
        role: SamplingMessageRole.user,
        content: SamplingTextContent(text: '请解释量子计算的基本原理'),
      ),
    ],
    modelPreferences: ModelPreferences(
      intelligencePriority: 0.8,
      costPriority: 0.2,
    ),
  ),
);
```

#### 5. 根目录系统 (Roots)
管理工作空间和权限：

```dart
// 客户端提供可访问的根目录
class ListRootsResult {
  final List<Root> roots;
}

class Root {
  final String uri;    // file:///path/to/workspace
  final String? name;  // 可选的友好名称
}
```

### 🚨 错误处理机制

#### 标准错误码

```dart
enum ErrorCode {
  parseError(-32700),      // JSON解析错误
  invalidRequest(-32600),  // 无效请求
  methodNotFound(-32601),  // 方法不存在
  invalidParams(-32602),   // 参数错误
  internalError(-32603),   // 内部错误
  connectionClosed(-32000), // 连接关闭
  requestTimeout(-32001),  // 请求超时
}
```

#### 自定义错误处理

```dart
// 抛出MCP错误
throw McpError(
  ErrorCode.invalidParams.value,
  '参数validation失败',
  {'field': 'email', 'error': 'invalid format'},
);

// 捕获和处理错误
try {
  await client.callTool(params);
} on McpError catch (e) {
  print('MCP错误 ${e.code}: ${e.message}');
  if (e.data != null) {
    print('详细信息: ${e.data}');
  }
} catch (e) {
  print('其他错误: $e');
}
```

### 🎯 最佳实践指南

1. **📋 始终声明正确的能力**
2. **🔄 使用适当的传输方式**
3. **⚡ 优雅处理错误和超时**
4. **🔒 实施适当的安全检查**
5. **📝 提供清晰的工具描述**
6. **🧪 编写全面的测试**
7. **📊 监控性能和资源使用**

---

## 客户端API

### 👤 Client类 - 核心客户端

`Client`类是MCP Dart客户端的核心，提供了完整的MCP协议功能。它继承自`Protocol`，并添加了客户端特有的功能。

#### 基本用法

```dart
import 'package:mcp_dart/mcp_dart.dart';

// 创建客户端实例
final client = Client(
  Implementation(
    name: "my-mcp-client",
    version: "1.0.0",
  ),
  options: ClientOptions(
    capabilities: ClientCapabilities(
      sampling: {},  // 启用LLM采样功能
      roots: ClientCapabilitiesRoots(listChanged: true),  // 启用根目录功能
    ),
  ),
);

// 连接到服务器
await client.connect(transport);

// 使用完成后关闭连接
await client.close();
```

#### 构造函数参数

```dart
Client(
  Implementation clientInfo,        // 客户端信息
  {ClientOptions? options}         // 可选配置
)
```

- **clientInfo**: 客户端实现信息
  - `name`: 客户端名称 (必需)
  - `version`: 客户端版本 (必需)
- **options**: 客户端选项
  - `capabilities`: 客户端能力声明
  - `protocolVersion`: 协议版本 (默认最新)

### 🔌 连接和生命周期管理

#### connect() - 连接到服务器

```dart
Future<void> connect(Transport transport) async
```

建立与MCP服务器的连接，执行初始化握手。

```dart
// Stdio传输示例
final transport = StdioClientTransport(
  StdioServerParameters(
    command: 'dart',
    args: ['run', 'my_server.dart'],
    workingDirectory: '/path/to/server',
    env: {'DEBUG': 'true'},
  ),
);

await client.connect(transport);
print('✅ 连接成功');
```

#### close() - 关闭连接

```dart
Future<void> close() async
```

关闭与服务器的连接，清理资源。

```dart
try {
  // 使用客户端...
} finally {
  await client.close();
  print('🔄 连接已关闭');
}
```

#### ping() - 心跳检测

```dart
Future<PingResult> ping() async
```

发送心跳消息检测连接状态。

```dart
try {
  final result = await client.ping();
  print('🟢 服务器在线');
} on McpError catch (e) {
  print('🔴 服务器无响应: ${e.message}');
}
```

### 🛠️ 工具相关API

#### listTools() - 列出可用工具

```dart
Future<ListToolsResult> listTools({String? cursor}) async
```

获取服务器提供的所有工具列表。

```dart
final toolsResult = await client.listTools();

print('可用工具数量: ${toolsResult.tools.length}');
for (final tool in toolsResult.tools) {
  print('工具: ${tool.name} - ${tool.description}');
  
  // 输出参数模式
  if (tool.inputSchema?.properties != null) {
    for (final param in tool.inputSchema!.properties!.entries) {
      print('  参数: ${param.key} (${param.value['type']})');
    }
  }
}

// 处理分页
if (toolsResult.nextCursor != null) {
  final nextPage = await client.listTools(cursor: toolsResult.nextCursor);
  // 处理下一页...
}
```

#### callTool() - 调用工具

```dart
Future<CallToolResult> callTool(CallToolRequestParams params) async
```

调用服务器上的特定工具。

```dart
// 基本工具调用
final result = await client.callTool(
  CallToolRequestParams(
    name: 'calculator',
    arguments: {
      'operation': 'add',
      'a': 10,
      'b': 5,
    },
  ),
);

// 处理结果
for (final content in result.content) {
  if (content is TextContent) {
    print('文本结果: ${content.text}');
  } else if (content is ImageContent) {
    print('图片结果: ${content.data}');
  }
}

// 检查是否有错误
if (result.isError == true) {
  print('工具执行出错');
}
```

### 📁 资源相关API

#### listResources() - 列出可用资源

```dart
Future<ListResourcesResult> listResources({String? cursor}) async
```

获取服务器提供的资源列表。

```dart
final resourcesResult = await client.listResources();

for (final resource in resourcesResult.resources) {
  print('资源: ${resource.name}');
  print('  URI: ${resource.uri}');
  print('  描述: ${resource.description}');
  print('  MIME类型: ${resource.mimeType ?? '未指定'}');
}
```

#### readResource() - 读取资源内容

```dart
Future<ReadResourceResult> readResource(ReadResourceRequestParams params) async
```

读取特定资源的内容。

```dart
// 读取静态资源
final resourceResult = await client.readResource(
  ReadResourceRequestParams(uri: 'file:///data/users.json'),
);

for (final content in resourceResult.contents) {
  if (content is TextResourceContents) {
    print('文本内容: ${content.text}');
    print('MIME类型: ${content.mimeType}');
  } else if (content is BlobResourceContents) {
    print('二进制内容大小: ${content.blob.length} bytes');
  }
}
```

#### subscribeToResource() - 订阅资源变更

```dart
Future<SubscribeToResourceResult> subscribeToResource(
  SubscribeToResourceRequestParams params
) async
```

订阅资源变更通知。

```dart
// 订阅资源变更
await client.subscribeToResource(
  SubscribeToResourceRequestParams(uri: 'file:///config.json'),
);

// 监听资源变更通知
client.onNotification.listen((notification) {
  if (notification is JsonRpcResourceUpdatedNotification) {
    print('资源已更新: ${notification.resourceParams.uri}');
    // 重新读取资源
  }
});
```

#### unsubscribeFromResource() - 取消订阅

```dart
Future<UnsubscribeFromResourceResult> unsubscribeFromResource(
  UnsubscribeFromResourceRequestParams params
) async
```

取消对特定资源的订阅。

```dart
await client.unsubscribeFromResource(
  UnsubscribeFromResourceRequestParams(uri: 'file:///config.json'),
);
```

#### listResourceTemplates() - 列出资源模板

```dart
Future<ListResourceTemplatesResult> listResourceTemplates({String? cursor}) async
```

获取动态资源模板列表。

```dart
final templatesResult = await client.listResourceTemplates();

for (final template in templatesResult.resourceTemplates) {
  print('模板: ${template.name}');
  print('  URI模板: ${template.uriTemplate}');
  print('  描述: ${template.description}');
}
```

### 💬 提示相关API

#### listPrompts() - 列出可用提示

```dart
Future<ListPromptsResult> listPrompts({String? cursor}) async
```

获取服务器提供的提示列表。

```dart
final promptsResult = await client.listPrompts();

for (final prompt in promptsResult.prompts) {
  print('提示: ${prompt.name}');
  print('  描述: ${prompt.description}');
  
  // 输出参数
  if (prompt.arguments != null) {
    for (final arg in prompt.arguments!) {
      print('  参数: ${arg.name} (${arg.required ? '必需' : '可选'})');
      print('    描述: ${arg.description}');
    }
  }
}
```

#### getPrompt() - 获取提示内容

```dart
Future<GetPromptResult> getPrompt(GetPromptRequestParams params) async
```

获取特定提示的内容。

```dart
final promptResult = await client.getPrompt(
  GetPromptRequestParams(
    name: 'code_review_prompt',
    arguments: {
      'language': 'dart',
      'style': 'strict',
    },
  ),
);

// 输出提示消息
for (final message in promptResult.messages) {
  print('角色: ${message.role}');
  
  if (message.content is TextContent) {
    final textContent = message.content as TextContent;
    print('内容: ${textContent.text}');
  }
}

// 输出提示描述
if (promptResult.description != null) {
  print('描述: ${promptResult.description}');
}
```

### 🎯 采样相关API (LLM交互)

#### createMessage() - 创建LLM消息

```dart
Future<CreateMessageResult> createMessage(CreateMessageRequestParams params) async
```

向LLM发送消息并获取响应 (需要客户端支持采样功能)。

```dart
final messageResult = await client.createMessage(
  CreateMessageRequestParams(
    messages: [
      SamplingMessage(
        role: SamplingMessageRole.user,
        content: SamplingTextContent(
          text: '请解释Dart中的异步编程概念',
        ),
      ),
    ],
    modelPreferences: ModelPreferences(
      costPriority: 0.3,
      speedPriority: 0.7,
      intelligencePriority: 0.9,
    ),
    systemPrompt: '你是一个Dart编程专家',
    includeContext: IncludeContext.thisServer,
    temperature: 0.7,
    maxTokens: 1000,
  ),
);

// 处理LLM响应
final response = messageResult.content;
if (response is SamplingTextContent) {
  print('LLM回复: ${response.text}');
}

print('使用的模型: ${messageResult.model}');
print('停止原因: ${messageResult.stopReason}');
```

### 📂 根目录相关API

#### listRoots() - 列出根目录

```dart
Future<ListRootsResult> listRoots() async
```

获取客户端可访问的根目录列表。

```dart
final rootsResult = await client.listRoots();

for (final root in rootsResult.roots) {
  print('根目录: ${root.name ?? root.uri}');
  print('  URI: ${root.uri}');
}
```

### 🎭 客户端传输实现

#### StdioClientTransport - 标准IO传输

用于启动和管理MCP服务器子进程，通过stdin/stdout通信。

```dart
class StdioClientTransport extends Transport {
  StdioClientTransport(StdioServerParameters serverParameters);
}

class StdioServerParameters {
  final String command;           // 可执行文件路径
  final List<String> args;        // 命令行参数
  final String? workingDirectory; // 工作目录
  final Map<String, String>? env; // 环境变量
}
```

**使用示例**:

```dart
// 基本用法
final transport = StdioClientTransport(
  StdioServerParameters(
    command: 'node',
    args: ['mcp-server.js'],
  ),
);

// 高级配置
final transport = StdioClientTransport(
  StdioServerParameters(
    command: 'python',
    args: ['-m', 'my_mcp_server'],
    workingDirectory: '/path/to/server',
    env: {
      'DEBUG': 'true',
      'CONFIG_PATH': '/etc/config.json',
    },
  ),
);

await client.connect(transport);
```

**特点**:
- ✅ 自动管理子进程生命周期
- ✅ 支持环境变量和工作目录设置
- ✅ 进程隔离，安全性高
- ⚠️ 仅限本地通信

#### StreamableHttpClientTransport - HTTP传输

用于通过HTTP协议连接远程MCP服务器。

```dart
class StreamableHttpClientTransport extends Transport {
  StreamableHttpClientTransport(
    Uri baseUri, 
    {HttpClientOptions? options}
  );
}

class HttpClientOptions {
  final Map<String, String>? headers;     // 自定义HTTP头
  final Duration? timeout;                // 请求超时
  final Duration? oauthTimeout;           // OAuth超时
  final bool Function(String)? shouldIncludeHeader; // 头部过滤
}
```

**使用示例**:

```dart
// 基本HTTP连接
final transport = StreamableHttpClientTransport(
  Uri.parse('https://api.example.com/mcp'),
);

// 带认证和配置
final transport = StreamableHttpClientTransport(
  Uri.parse('https://api.example.com/mcp'),
  options: HttpClientOptions(
    headers: {
      'Authorization': 'Bearer $token',
      'X-Client-Version': '1.0.0',
    },
    timeout: Duration(seconds: 30),
    shouldIncludeHeader: (name) => !name.startsWith('x-internal-'),
  ),
);

await client.connect(transport);
```

**OAuth认证流程**:

```dart
final transport = StreamableHttpClientTransport(
  Uri.parse('https://api.example.com/mcp'),
);

// OAuth认证会自动处理
await client.connect(transport);

// 检查会话状态
if (transport.sessionId != null) {
  print('会话ID: ${transport.sessionId}');
}
```

**特点**:
- ✅ 支持远程连接
- ✅ 自动OAuth认证流程
- ✅ 会话管理和重连
- ✅ 防火墙友好
- ⚠️ 需要网络连接

### 🔄 事件监听

#### 通知监听

```dart
// 监听所有通知
client.onNotification.listen((notification) {
  if (notification is JsonRpcResourceUpdatedNotification) {
    print('资源更新: ${notification.resourceParams.uri}');
  } else if (notification is JsonRpcRootsListChangedNotification) {
    print('根目录列表已更改');
  }
});

// 监听特定类型的通知
client.onNotification
    .where((n) => n is JsonRpcProgressNotification)
    .listen((notification) {
  final progress = notification as JsonRpcProgressNotification;
  print('进度: ${progress.progressParams.progress}/${progress.progressParams.total}');
});
```

#### 连接状态监听

```dart
// 监听连接状态变化
client.onConnectionStateChanged.listen((state) {
  switch (state) {
    case ConnectionState.connected:
      print('✅ 已连接');
      break;
    case ConnectionState.disconnected:
      print('🔴 已断开');
      break;
    case ConnectionState.connecting:
      print('🔄 连接中...');
      break;
  }
});
```

### 🚨 错误处理和超时

#### 超时配置

```dart
// 设置请求超时
final client = Client(
  Implementation(name: "client", version: "1.0.0"),
  options: ClientOptions(
    requestTimeout: Duration(seconds: 10),
  ),
);
```

#### 错误处理模式

```dart
try {
  final result = await client.callTool(params);
  // 处理成功结果
} on McpError catch (e) {
  // MCP协议错误
  switch (e.code) {
    case -32601: // Method not found
      print('工具不存在: ${e.message}');
      break;
    case -32602: // Invalid params  
      print('参数错误: ${e.message}');
      break;
    default:
      print('MCP错误 ${e.code}: ${e.message}');
  }
} on TimeoutException {
  print('请求超时');
} catch (e) {
  // 其他错误
  print('未知错误: $e');
}
```

### 💡 客户端最佳实践

#### 1. 资源管理

```dart
class McpClientManager {
  Client? _client;
  
  Future<void> initialize() async {
    _client = Client(Implementation(name: "app", version: "1.0.0"));
    await _client!.connect(transport);
  }
  
  Future<void> dispose() async {
    await _client?.close();
    _client = null;
  }
}
```

#### 2. 错误重试

```dart
Future<T> retryRequest<T>(Future<T> Function() request, {int maxRetries = 3}) async {
  for (int i = 0; i < maxRetries; i++) {
    try {
      return await request();
    } catch (e) {
      if (i == maxRetries - 1) rethrow;
      await Future.delayed(Duration(seconds: math.pow(2, i).toInt()));
    }
  }
  throw StateError('Should not reach here');
}

// 使用
final result = await retryRequest(() => client.callTool(params));
```

#### 3. 能力检查

```dart
 Future<void> ensureCapabilities() async {
   final caps = client.serverCapabilities;
   
   if (caps?.tools == null) {
     throw StateError('服务器不支持工具功能');
   }
   
   if (caps?.resources?.subscribe != true) {
     print('⚠️ 服务器不支持资源订阅');
   }
 }
 ```

---

## 服务器API

### 🖥️ McpServer类 - 高级服务器

`McpServer`类是MCP Dart服务器端的高级API，提供了简单易用的接口来实现MCP服务器功能。它封装了底层的复杂性，让开发者可以专注于业务逻辑。

#### 基本用法

```dart
import 'package:mcp_dart/mcp_dart.dart';

// 创建服务器实例
final server = McpServer(
  Implementation(
    name: "my-mcp-server",
    version: "1.0.0",
  ),
  options: ServerOptions(
    capabilities: ServerCapabilities(
      tools: ServerCapabilitiesTools(),
      resources: ServerCapabilitiesResources(subscribe: true),
      prompts: ServerCapabilitiesPrompts(),
      logging: ServerCapabilitiesLogging(),
    ),
  ),
);

// 启动服务器
await server.connect(StdioServerTransport());
```

#### 构造函数参数

```dart
McpServer(
  Implementation serverInfo,        // 服务器信息
  {ServerOptions? options}         // 可选配置
)
```

- **serverInfo**: 服务器实现信息
  - `name`: 服务器名称 (必需)
  - `version`: 服务器版本 (必需)
- **options**: 服务器选项
  - `capabilities`: 服务器能力声明
  - `protocolVersion`: 协议版本 (默认最新)

### 🛠️ 工具注册和管理

#### tool() - 注册工具

```dart
void tool(
  String name,
  {String? description,
   Map<String, dynamic>? inputSchemaProperties,
   List<String>? inputSchemaRequired,
   ToolCallback? callback}
)
```

注册一个新的工具供客户端调用。

```dart
// 简单工具
server.tool(
  'greet',
  description: '友好问候工具',
  inputSchemaProperties: {
    'name': {'type': 'string', 'description': '要问候的姓名'},
    'language': {'type': 'string', 'enum': ['zh', 'en'], 'default': 'zh'},
  },
  inputSchemaRequired: ['name'],
  callback: ({args, extra}) async {
    final name = args!['name'] as String;
    final language = args['language'] as String? ?? 'zh';
    
    final greeting = language == 'zh' ? '你好, $name!' : 'Hello, $name!';
    
    return CallToolResult.fromContent(
      content: [TextContent(text: greeting)],
    );
  },
);

// 复杂工具 - 文件操作
server.tool(
  'file_operations',
  description: '文件系统操作工具',
  inputSchemaProperties: {
    'operation': {
      'type': 'string',
      'enum': ['read', 'write', 'delete', 'list'],
      'description': '操作类型'
    },
    'path': {'type': 'string', 'description': '文件路径'},
    'content': {'type': 'string', 'description': '写入内容(仅write操作)'},
  },
  inputSchemaRequired: ['operation', 'path'],
  callback: ({args, extra}) async {
    final operation = args!['operation'] as String;
    final path = args['path'] as String;
    
    switch (operation) {
      case 'read':
        final content = await File(path).readAsString();
        return CallToolResult.fromContent(
          content: [TextContent(text: content)],
        );
        
      case 'write':
        final content = args['content'] as String;
        await File(path).writeAsString(content);
        return CallToolResult.fromContent(
          content: [TextContent(text: '文件写入成功')],
        );
        
      case 'list':
        final dir = Directory(path);
        final files = await dir.list().map((e) => e.path).toList();
        return CallToolResult.fromContent(
          content: [TextContent(text: files.join('\n'))],
        );
        
      default:
        throw McpError(
          ErrorCode.invalidParams.value,
          '不支持的操作: $operation',
        );
    }
  },
);
```

#### ToolCallback类型定义

```dart
typedef ToolCallback = Future<CallToolResult> Function({
  Map<String, dynamic>? args,    // 工具参数
  CallToolExtra? extra,          // 额外信息
});

class CallToolExtra {
  final ProgressCallback? onProgress;  // 进度回调
  final bool isCancel;                // 是否取消
}
```

#### 工具回调中的进度报告

```dart
server.tool(
  'long_running_task',
  description: '长时间运行的任务',
  callback: ({args, extra}) async {
    final total = 100;
    
    for (int i = 0; i <= total; i += 10) {
      // 报告进度
      extra?.onProgress?.call(
        JsonRpcProgressNotification(
          progressParams: ProgressRequestParams(
            progressToken: 'task_${DateTime.now().millisecondsSinceEpoch}',
            progress: i,
            total: total,
          ),
        ),
      );
      
      // 模拟工作
      await Future.delayed(Duration(milliseconds: 500));
      
      // 检查是否被取消
      if (extra?.isCancel == true) {
        throw McpError(
          ErrorCode.requestCancelled.value,
          '任务已被取消',
        );
      }
    }
    
    return CallToolResult.fromContent(
      content: [TextContent(text: '任务完成')],
    );
  },
);
```

### 📁 资源注册和管理

#### resource() - 注册静态资源

```dart
void resource(
  String name,
  String uri,
  ResourceCallback callback,
  {String? description, String? mimeType}
)
```

注册一个静态资源。

```dart
// 配置文件资源
server.resource(
  'config',
  'file:///app/config.json',
  (uri, extra) async {
    final configFile = File.fromUri(uri);
    if (!await configFile.exists()) {
      throw McpError(
        ErrorCode.resourceNotFound.value,
        '配置文件不存在: ${uri.path}',
      );
    }
    
    final content = await configFile.readAsString();
    return ReadResourceResult(
      contents: [
        TextResourceContents(
          uri: uri.toString(),
          text: content,
          mimeType: 'application/json',
        ),
      ],
    );
  },
  description: '应用程序配置文件',
  mimeType: 'application/json',
);

// 二进制资源
server.resource(
  'avatar',
  'file:///images/avatar.png',
  (uri, extra) async {
    final imageFile = File.fromUri(uri);
    final bytes = await imageFile.readAsBytes();
    
    return ReadResourceResult(
      contents: [
        BlobResourceContents(
          uri: uri.toString(),
          blob: bytes,
          mimeType: 'image/png',
        ),
      ],
    );
  },
  description: '用户头像图片',
  mimeType: 'image/png',
);
```

#### resourceTemplate() - 注册动态资源模板

```dart
void resourceTemplate(
  String name,
  ResourceTemplateRegistration template,
  ResourceTemplateCallback callback,
  {String? description, String? mimeType}
)
```

注册一个动态资源模板，支持URI变量替换。

```dart
// 用户资料模板
server.resourceTemplate(
  'user_profile',
  ResourceTemplateRegistration('user://profile/{userId}'),
  (uri, variables, extra) async {
    final userId = variables['userId'];
    if (userId == null) {
      throw McpError(
        ErrorCode.invalidParams.value,
        '缺少用户ID参数',
      );
    }
    
    // 从数据库获取用户信息
    final user = await getUserFromDatabase(userId);
    if (user == null) {
      throw McpError(
        ErrorCode.resourceNotFound.value,
        '用户不存在: $userId',
      );
    }
    
    final profile = jsonEncode({
      'id': user.id,
      'name': user.name,
      'email': user.email,
      'created_at': user.createdAt.toIso8601String(),
    });
    
    return ReadResourceResult(
      contents: [
        TextResourceContents(
          uri: uri.toString(),
          text: profile,
          mimeType: 'application/json',
        ),
      ],
    );
  },
  description: '用户资料信息',
  mimeType: 'application/json',
);

// 文件系统浏览模板
server.resourceTemplate(
  'filesystem',
  ResourceTemplateRegistration('file:///{path*}'),
  (uri, variables, extra) async {
    final path = variables['path*'] ?? '';
    final file = File(path);
    final dir = Directory(path);
    
    if (await file.exists()) {
      // 返回文件内容
      final content = await file.readAsString();
      return ReadResourceResult(
        contents: [
          TextResourceContents(
            uri: uri.toString(),
            text: content,
            mimeType: _getMimeType(path),
          ),
        ],
      );
    } else if (await dir.exists()) {
      // 返回目录列表
      final entries = await dir.list().map((e) => e.path).toList();
      return ReadResourceResult(
        contents: [
          TextResourceContents(
            uri: uri.toString(),
            text: entries.join('\n'),
            mimeType: 'text/plain',
          ),
        ],
      );
    } else {
      throw McpError(
        ErrorCode.resourceNotFound.value,
        '路径不存在: $path',
      );
    }
  },
  description: '文件系统访问',
);
```

#### 资源变更通知

```dart
// 资源变更时发送通知
void notifyResourceChanged(String uri) {
  server.sendNotification(
    JsonRpcResourceUpdatedNotification(
      resourceParams: ResourceUpdatedRequestParams(uri: uri),
    ),
  );
}

// 监听文件变化示例
void setupFileWatcher(String path) {
  final watcher = File(path).watch();
  watcher.listen((event) {
    if (event.type == FileSystemEvent.modify) {
      notifyResourceChanged('file://$path');
    }
  });
}
```

### 💬 提示注册和管理

#### prompt() - 注册提示

```dart
void prompt(
  String name,
  {String? description,
   Map<String, PromptArgumentDefinition>? argsSchema,
   PromptCallback? callback}
)
```

注册一个提示生成器。

```dart
// 代码审查提示
server.prompt(
  'code_review',
  description: '生成代码审查提示',
  argsSchema: {
    'language': PromptArgumentDefinition(
      type: String,
      description: '编程语言',
      required: true,
    ),
    'style': PromptArgumentDefinition(
      type: String,
      description: '审查风格',
      required: false,
    ),
    'focus': PromptArgumentDefinition(
      type: List<String>,
      description: '关注点',
      required: false,
    ),
  },
  callback: (args, extra) async {
    final language = args['language'] as String;
    final style = args['style'] as String? ?? 'balanced';
    final focus = args['focus'] as List<String>? ?? [];
    
    String prompt = '请对以下$language代码进行审查。\n\n';
    
    switch (style) {
      case 'strict':
        prompt += '审查标准：严格模式，关注所有潜在问题。\n';
        break;
      case 'performance':
        prompt += '审查标准：主要关注性能优化。\n';
        break;
      case 'security':
        prompt += '审查标准：主要关注安全问题。\n';
        break;
      default:
        prompt += '审查标准：平衡模式，关注主要问题。\n';
    }
    
    if (focus.isNotEmpty) {
      prompt += '特别关注：${focus.join(', ')}\n';
    }
    
    prompt += '\n审查要点：\n';
    prompt += '1. 代码质量和可读性\n';
    prompt += '2. 性能优化建议\n';
    prompt += '3. 潜在的bug和问题\n';
    prompt += '4. 最佳实践建议\n\n';
    prompt += '请提供具体的改进建议。\n';
    
    return GetPromptResult(
      description: '针对$language的$style风格代码审查提示',
      messages: [
        PromptMessage(
          role: PromptMessageRole.user,
          content: TextContent(text: prompt),
        ),
      ],
    );
  },
);

// 数据分析提示
server.prompt(
  'data_analysis',
  description: '生成数据分析提示',
  argsSchema: {
    'dataset': PromptArgumentDefinition(
      type: String,
      description: '数据集描述',
      required: true,
    ),
    'goal': PromptArgumentDefinition(
      type: String,
      description: '分析目标',
      required: true,
    ),
    'methods': PromptArgumentDefinition(
      type: List<String>,
      description: '分析方法',
      required: false,
    ),
  },
  callback: (args, extra) async {
    final dataset = args['dataset'] as String;
    final goal = args['goal'] as String;
    final methods = args['methods'] as List<String>? ?? ['descriptive', 'inferential'];
    
    String prompt = '数据分析任务：\n\n';
    prompt += '数据集：$dataset\n';
    prompt += '分析目标：$goal\n';
    prompt += '推荐方法：${methods.join(', ')}\n\n';
    prompt += '请提供详细的分析步骤和可视化建议。';
    
    return GetPromptResult(
      description: '数据分析指导提示',
      messages: [
        PromptMessage(
          role: PromptMessageRole.user,
          content: TextContent(text: prompt),
        ),
      ],
    );
  },
);
```

### 🎯 采样请求处理

MCP服务器可以向客户端发送采样请求，让LLM处理特定任务：

```dart
// 向客户端请求LLM采样
Future<CreateMessageResult> requestLLMSampling({
  required List<SamplingMessage> messages,
  String? systemPrompt,
  ModelPreferences? modelPreferences,
  double? temperature,
  int? maxTokens,
}) async {
  final request = JsonRpcCreateMessageRequest(
    id: generateUUID(),
    createParams: CreateMessageRequestParams(
      messages: messages,
      systemPrompt: systemPrompt,
      modelPreferences: modelPreferences,
      temperature: temperature,
      maxTokens: maxTokens,
      includeContext: IncludeContext.thisServer,
    ),
  );
  
  return await server.sendRequest(request);
}

// 在工具中使用LLM
server.tool(
  'smart_summarize',
  description: 'AI智能摘要工具',
  inputSchemaProperties: {
    'text': {'type': 'string', 'description': '要摘要的文本'},
    'length': {'type': 'string', 'enum': ['short', 'medium', 'long'], 'default': 'medium'},
  },
  callback: ({args, extra}) async {
    final text = args!['text'] as String;
    final length = args['length'] as String? ?? 'medium';
    
    // 构建LLM提示
    final lengthInstruction = {
      'short': '请生成简短摘要（1-2句话）',
      'medium': '请生成中等长度摘要（3-5句话）',
      'long': '请生成详细摘要（一段话）',
    }[length]!;
    
    // 请求LLM处理
    final result = await requestLLMSampling(
      messages: [
        SamplingMessage(
          role: SamplingMessageRole.user,
          content: SamplingTextContent(
            text: '$lengthInstruction：\n\n$text',
          ),
        ),
      ],
      systemPrompt: '你是一个专业的文本摘要助手。',
      temperature: 0.3,
      maxTokens: 200,
    );
    
    // 返回摘要结果
    if (result.content is SamplingTextContent) {
      final summary = (result.content as SamplingTextContent).text;
      return CallToolResult.fromContent(
        content: [TextContent(text: summary)],
      );
    } else {
      throw McpError(
        ErrorCode.internalError.value,
        'LLM返回了非文本内容',
      );
    }
  },
);
```

### 📊 日志系统

#### 日志级别和格式

```dart
enum LogLevel {
  debug,
  info, 
  warning,
  error,
}

// 发送日志到客户端
void logMessage(LogLevel level, String message, {Map<String, dynamic>? data}) {
  server.sendNotification(
    JsonRpcLogMessageNotification(
      logParams: LogMessageRequestParams(
        level: level.name,
        data: data,
        logger: 'my-server',
      ),
    ),
  );
}

// 结构化日志
void logStructured(String event, Map<String, dynamic> data) {
  logMessage(
    LogLevel.info,
    event,
    data: {
      'timestamp': DateTime.now().toIso8601String(),
      'event': event,
      ...data,
    },
  );
}

// 工具中的日志记录
server.tool(
  'database_query',
  callback: ({args, extra}) async {
    final query = args!['query'] as String;
    
    // 记录查询开始
    logStructured('query_start', {
      'query': query,
      'user': args['user'] ?? 'anonymous',
    });
    
    try {
      final result = await database.query(query);
      
      // 记录查询成功
      logStructured('query_success', {
        'query': query,
        'rows_returned': result.length,
        'duration_ms': stopwatch.elapsedMilliseconds,
      });
      
      return CallToolResult.fromContent(
        content: [TextContent(text: jsonEncode(result))],
      );
      
    } catch (e) {
      // 记录查询错误
      logMessage(LogLevel.error, 'Database query failed', {
        'query': query,
        'error': e.toString(),
      });
      rethrow;
    }
  },
);
```

### 🎭 服务器传输实现

#### StdioServerTransport - 标准IO传输

最简单的传输方式，通过标准输入输出通信：

```dart
// 基本用法
await server.connect(StdioServerTransport());

// 服务器会自动处理stdin的JSON-RPC消息
// 并通过stdout发送响应
```

**特点**:
- ✅ 简单可靠，适合命令行应用
- ✅ 自动处理JSON-RPC消息格式
- ✅ 跨平台支持
- ⚠️ 仅限子进程模式

#### SseServerTransport - SSE传输

用于Web环境的实时通信：

```dart
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';

// 创建SSE服务器管理器
final manager = SseServerManager(server);

// 设置HTTP服务器
final handler = Pipeline()
    .addMiddleware(logRequests())
    .addHandler((request) async {
  
  // SSE连接端点
  if (request.url.path == 'sse') {
    return await manager.handleSseRequest(request);
  }
  
  // 消息发送端点
  if (request.url.path == 'message' && request.method == 'POST') {
    return await manager.handleMessageRequest(request);
  }
  
  return Response.notFound('Not found');
});

// 启动HTTP服务器
await serve(handler, InternetAddress.anyIPv4, 8080);
print('🌐 SSE服务器运行在 http://localhost:8080');
```

**特点**:
- ✅ 支持Web浏览器客户端
- ✅ 实时双向通信
- ✅ HTTP标准协议
- ⚠️ 需要HTTP服务器基础设施

#### StreamableHTTPServerTransport - HTTP传输

企业级HTTP传输实现：

```dart
final transport = StreamableHTTPServerTransport(
  options: StreamableHTTPServerTransportOptions(
    sessionIdGenerator: () => generateUUID(),
    allowedOrigins: ['https://app.example.com'],
    authenticationRequired: true,
  ),
);

// 集成到HTTP框架
app.post('/mcp/messages', (request, response) async {
  await transport.handleRequest(request, response);
});

app.get('/mcp/sse/:sessionId', (request, response) async {
  final sessionId = request.params['sessionId'];
  await transport.handleSseConnection(sessionId, response);
});
```

**特点**:
- ✅ 企业级功能：认证、CORS、会话管理
- ✅ 可扩展性好
- ✅ 负载均衡友好
- ⚠️ 配置相对复杂

### 🔧 Server类 - 底层服务器

`Server`类提供了更底层的控制，适合需要自定义协议处理的场景：

```dart
final server = Server(Implementation(name: "custom-server", version: "1.0.0"));

// 自定义请求处理器
server.setRequestHandler<JsonRpcCallToolRequest>('tools/call', (request) async {
  final toolName = request.callParams.name;
  final args = request.callParams.arguments;
  
  // 自定义工具调用逻辑
  switch (toolName) {
    case 'custom_tool':
      return CallToolResult.fromContent(
        content: [TextContent(text: 'Custom response')],
      );
    default:
      throw McpError(
        ErrorCode.methodNotFound.value,
        'Unknown tool: $toolName',
      );
  }
});

// 自定义通知处理器
server.setNotificationHandler<JsonRpcInitializedNotification>('initialized', (notification) {
  print('客户端初始化完成');
});
```

### 💡 服务器最佳实践

#### 1. 错误处理模式

```dart
server.tool(
  'safe_tool',
  callback: ({args, extra}) async {
    try {
      // 业务逻辑
      return await processRequest(args);
    } on ValidationException catch (e) {
      throw McpError(
        ErrorCode.invalidParams.value,
        e.message,
        {'field': e.field, 'value': e.value},
      );
    } on AuthenticationException catch (e) {
      throw McpError(
        ErrorCode.unauthorizedAccess.value,
        '认证失败',
      );
    } catch (e) {
      // 记录内部错误
      logMessage(LogLevel.error, 'Internal error', {'error': e.toString()});
      
      throw McpError(
        ErrorCode.internalError.value,
        '服务器内部错误',
      );
    }
  },
);
```

#### 2. 参数验证

```dart
Map<String, dynamic> validateArgs(Map<String, dynamic>? args, Map<String, String> schema) {
  if (args == null) {
    throw McpError(ErrorCode.invalidParams.value, '缺少参数');
  }
  
  final validated = <String, dynamic>{};
  
  for (final entry in schema.entries) {
    final key = entry.key;
    final type = entry.value;
    
    if (!args.containsKey(key)) {
      throw McpError(ErrorCode.invalidParams.value, '缺少必需参数: $key');
    }
    
    final value = args[key];
    
    switch (type) {
      case 'string':
        if (value is! String) {
          throw McpError(ErrorCode.invalidParams.value, '参数$key必须是字符串');
        }
        break;
      case 'number':
        if (value is! num) {
          throw McpError(ErrorCode.invalidParams.value, '参数$key必须是数字');
        }
        break;
      // 更多类型检查...
    }
    
    validated[key] = value;
  }
  
  return validated;
}
```

#### 3. 资源管理

```dart
 class ResourceManager {
   final Map<String, StreamSubscription> _watchers = {};
   
   void watchResource(String uri, ResourceCallback callback) {
     final file = File.fromUri(Uri.parse(uri));
     final subscription = file.watch().listen((event) {
       server.sendNotification(
         JsonRpcResourceUpdatedNotification(
           resourceParams: ResourceUpdatedRequestParams(uri: uri),
         ),
       );
     });
     
     _watchers[uri] = subscription;
   }
   
   void dispose() {
     for (final subscription in _watchers.values) {
       subscription.cancel();
     }
     _watchers.clear();
   }
 }

---

## 传输层

### 🔄 Transport接口 - 传输抽象

所有传输实现都基于`Transport`抽象接口：

```dart
abstract class Transport {
  Stream<JsonRpcMessage> get messageStream;  // 接收消息流
  void sendMessage(JsonRpcMessage message);  // 发送消息
  Future<void> connect();                    // 建立连接
  Future<void> close();                      // 关闭连接
}
```

### 🔧 IOStreamTransport - 内存流传输

最基础的传输实现，适合测试和同进程通信：

```dart
// 创建内存流传输
final controller = StreamController<List<int>>();
final transport = IOStreamTransport(
  stream: controller.stream,
  sink: controller.sink,
);

// 测试用法
void testMcpCommunication() async {
  // 创建模拟的消息流
  final inputController = StreamController<List<int>>();
  final outputController = StreamController<List<int>>();
  
  // 服务器端传输
  final serverTransport = IOStreamTransport(
    stream: inputController.stream,
    sink: outputController.sink,
  );
  
  // 客户端传输  
  final clientTransport = IOStreamTransport(
    stream: outputController.stream,
    sink: inputController.sink,
  );
  
  // 连接服务器和客户端
  final server = McpServer(Implementation(name: "test-server", version: "1.0.0"));
  final client = Client(Implementation(name: "test-client", version: "1.0.0"));
  
  await server.connect(serverTransport);
  await client.connect(clientTransport);
  
  // 进行测试...
}
```

### 📡 消息编解码

所有传输层都使用统一的消息编解码机制：

```dart
// JSON-RPC消息编码
List<int> encodeMessage(JsonRpcMessage message) {
  final json = message.toJson();
  final jsonString = jsonEncode(json);
  return utf8.encode('$jsonString\n');
}

// JSON-RPC消息解码
JsonRpcMessage? decodeMessage(String line) {
  try {
    final json = jsonDecode(line) as Map<String, dynamic>;
    return JsonRpcMessage.fromJson(json);
  } catch (e) {
    // 解码错误处理
    return null;
  }
}
```

---

## 数据类型

### 📋 核心协议类型

#### Implementation - 实现信息

```dart
class Implementation {
  final String name;     // 名称
  final String version;  // 版本
  
  const Implementation({
    required this.name,
    required this.version,
  });
}
```

#### ServerCapabilities - 服务器能力

```dart
class ServerCapabilities {
  final ServerCapabilitiesTools? tools;           // 工具支持
  final ServerCapabilitiesResources? resources;   // 资源支持
  final ServerCapabilitiesPrompts? prompts;       // 提示支持
  final ServerCapabilitiesLogging? logging;       // 日志支持
  
  const ServerCapabilities({
    this.tools,
    this.resources, 
    this.prompts,
    this.logging,
  });
}

class ServerCapabilitiesResources {
  final bool? subscribe;     // 是否支持资源订阅
  final bool? listChanged;   // 是否支持列表变更通知
  
  const ServerCapabilitiesResources({
    this.subscribe,
    this.listChanged,
  });
}
```

#### ClientCapabilities - 客户端能力

```dart
class ClientCapabilities {
  final Map<String, dynamic>? sampling;           // 采样支持
  final ClientCapabilitiesRoots? roots;          // 根目录支持
  
  const ClientCapabilities({
    this.sampling,
    this.roots,
  });
}
```

### 🛠️ 工具相关类型

#### Tool - 工具定义

```dart
class Tool {
  final String name;                    // 工具名称
  final String? description;            // 工具描述
  final ToolInputSchema? inputSchema;   // 输入参数模式
  
  const Tool({
    required this.name,
    this.description,
    this.inputSchema,
  });
}

class ToolInputSchema {
  final String type;                           // 类型 (通常是 "object")
  final Map<String, dynamic>? properties;     // 属性定义
  final List<String>? required;               // 必需属性
  
  const ToolInputSchema({
    required this.type,
    this.properties,
    this.required,
  });
}
```

#### CallToolRequestParams - 工具调用参数

```dart
class CallToolRequestParams {
  final String name;                        // 工具名称
  final Map<String, dynamic>? arguments;    // 调用参数
  
  const CallToolRequestParams({
    required this.name,
    this.arguments,
  });
}
```

#### CallToolResult - 工具调用结果

```dart
class CallToolResult {
  final List<Content> content;     // 返回内容
  final bool? isError;            // 是否出错
  
  const CallToolResult({
    required this.content,
    this.isError,
  });
  
  // 便捷构造函数
  factory CallToolResult.fromContent({required List<Content> content}) {
    return CallToolResult(content: content);
  }
  
  factory CallToolResult.fromText(String text) {
    return CallToolResult(content: [TextContent(text: text)]);
  }
  
  factory CallToolResult.error(String errorMessage) {
    return CallToolResult(
      content: [TextContent(text: errorMessage)],
      isError: true,
    );
  }
}
```

### 📁 资源相关类型

#### Resource - 资源定义

```dart
class Resource {
  final String uri;             // 资源URI
  final String name;            // 资源名称
  final String? description;    // 资源描述
  final String? mimeType;       // MIME类型
  
  const Resource({
    required this.uri,
    required this.name,
    this.description,
    this.mimeType,
  });
}
```

#### ResourceContents - 资源内容

```dart
// 文本资源内容
class TextResourceContents extends ResourceContents {
  final String uri;
  final String text;
  final String? mimeType;
  
  const TextResourceContents({
    required this.uri,
    required this.text,
    this.mimeType,
  });
}

// 二进制资源内容
class BlobResourceContents extends ResourceContents {
  final String uri;
  final List<int> blob;
  final String? mimeType;
  
  const BlobResourceContents({
    required this.uri,
    required this.blob,
    this.mimeType,
  });
}
```

### 💬 内容类型系统

#### Content - 内容基类

```dart
abstract class Content {
  const Content();
}

// 文本内容
class TextContent extends Content {
  final String text;
  
  const TextContent({required this.text});
}

// 图片内容
class ImageContent extends Content {
  final String data;        // Base64编码的图片数据
  final String mimeType;    // 图片MIME类型
  
  const ImageContent({
    required this.data,
    required this.mimeType,
  });
}

// 嵌入内容
class EmbeddedResource extends Content {
  final String resource;    // 资源标识
  final Map<String, dynamic>? annotations;  // 注释
  
  const EmbeddedResource({
    required this.resource,
    this.annotations,
  });
}
```

### 🎯 采样相关类型

#### SamplingMessage - 采样消息

```dart
class SamplingMessage {
  final SamplingMessageRole role;
  final SamplingContent content;
  
  const SamplingMessage({
    required this.role,
    required this.content,
  });
}

enum SamplingMessageRole {
  assistant,
  user,
}

// 采样文本内容
class SamplingTextContent extends SamplingContent {
  final String text;
  
  const SamplingTextContent({required this.text});
}

// 采样图片内容
class SamplingImageContent extends SamplingContent {
  final String data;
  final String mimeType;
  
  const SamplingImageContent({
    required this.data,
    required this.mimeType,
  });
}
```

#### ModelPreferences - 模型偏好

```dart
class ModelPreferences {
  final double? costPriority;         // 成本优先级 (0.0-1.0)
  final double? speedPriority;        // 速度优先级 (0.0-1.0) 
  final double? intelligencePriority; // 智能优先级 (0.0-1.0)
  
  const ModelPreferences({
    this.costPriority,
    this.speedPriority,
    this.intelligencePriority,
  });
}
```

### 🚨 错误处理类型

#### McpError - MCP错误

```dart
class McpError implements Exception {
  final int code;                       // 错误码
  final String message;                 // 错误消息
  final Map<String, dynamic>? data;     // 额外数据
  
  const McpError(this.code, this.message, [this.data]);
  
  @override
  String toString() => 'McpError($code): $message';
}
```

#### 标准错误码

```dart
enum ErrorCode {
  // JSON-RPC标准错误
  parseError(-32700),          // JSON解析错误
  invalidRequest(-32600),      // 无效请求
  methodNotFound(-32601),      // 方法不存在
  invalidParams(-32602),       // 无效参数
  internalError(-32603),       // 内部错误
  
  // MCP特定错误
  connectionClosed(-32000),    // 连接已关闭
  requestTimeout(-32001),      // 请求超时
  requestCancelled(-32002),    // 请求被取消
  resourceNotFound(-32003),    // 资源不存在
  unauthorizedAccess(-32004),  // 未授权访问
}

---

## 示例代码

### 🎯 完整项目示例

#### 天气服务MCP服务器

```dart
// weather_server.dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:mcp_dart/mcp_dart.dart';

class WeatherService {
  static const String apiKey = 'your-api-key';
  static const String baseUrl = 'https://api.openweathermap.org/data/2.5';
  
  Future<Map<String, dynamic>> getCurrentWeather(String city) async {
    final url = '$baseUrl/weather?q=$city&appid=$apiKey&units=metric';
    final response = await http.get(Uri.parse(url));
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get weather data');
    }
  }
  
  Future<Map<String, dynamic>> getForecast(String city, int days) async {
    final url = '$baseUrl/forecast?q=$city&appid=$apiKey&units=metric&cnt=${days * 8}';
    final response = await http.get(Uri.parse(url));
    
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to get forecast data');
    }
  }
}

void main() async {
  final weatherService = WeatherService();
  
  final server = McpServer(
    Implementation(name: "weather-server", version: "1.0.0"),
    options: ServerOptions(
      capabilities: ServerCapabilities(
        tools: ServerCapabilitiesTools(),
        resources: ServerCapabilitiesResources(subscribe: true),
        logging: ServerCapabilitiesLogging(),
      ),
    ),
  );

  // 注册当前天气工具
  server.tool(
    'get_current_weather',
    description: '获取指定城市的当前天气信息',
    inputSchemaProperties: {
      'city': {
        'type': 'string',
        'description': '城市名称（英文）',
      },
      'include_details': {
        'type': 'boolean',
        'description': '是否包含详细信息',
        'default': false,
      },
    },
    inputSchemaRequired: ['city'],
    callback: ({args, extra}) async {
      try {
        final city = args!['city'] as String;
        final includeDetails = args['include_details'] as bool? ?? false;
        
        final weatherData = await weatherService.getCurrentWeather(city);
        
        final temperature = weatherData['main']['temp'];
        final description = weatherData['weather'][0]['description'];
        final humidity = weatherData['main']['humidity'];
        final windSpeed = weatherData['wind']['speed'];
        
        String result = '$city当前天气：\n';
        result += '温度：${temperature}°C\n';
        result += '描述：$description\n';
        
        if (includeDetails) {
          result += '湿度：$humidity%\n';
          result += '风速：${windSpeed}m/s\n';
          result += '气压：${weatherData['main']['pressure']}hPa\n';
        }
        
        return CallToolResult.fromContent(
          content: [TextContent(text: result)],
        );
        
      } catch (e) {
        throw McpError(
          ErrorCode.internalError.value,
          '获取天气信息失败: $e',
        );
      }
    },
  );

  // 注册天气预报工具
  server.tool(
    'get_weather_forecast',
    description: '获取指定城市的天气预报',
    inputSchemaProperties: {
      'city': {
        'type': 'string',
        'description': '城市名称（英文）',
      },
      'days': {
        'type': 'integer',
        'description': '预报天数(1-5)',
        'minimum': 1,
        'maximum': 5,
        'default': 3,
      },
    },
    inputSchemaRequired: ['city'],
    callback: ({args, extra}) async {
      try {
        final city = args!['city'] as String;
        final days = args['days'] as int? ?? 3;
        
        final forecastData = await weatherService.getForecast(city, days);
        final forecasts = forecastData['list'] as List;
        
        String result = '$city未来${days}天天气预报：\n\n';
        
        for (int i = 0; i < days && i * 8 < forecasts.length; i++) {
          final dayForecast = forecasts[i * 8];
          final date = DateTime.fromMillisecondsSinceEpoch(
            dayForecast['dt'] * 1000,
          );
          final temp = dayForecast['main']['temp'];
          final desc = dayForecast['weather'][0]['description'];
          
          result += '${date.month}/${date.day}：${temp}°C，$desc\n';
        }
        
        return CallToolResult.fromContent(
          content: [TextContent(text: result)],
        );
        
      } catch (e) {
        throw McpError(
          ErrorCode.internalError.value,
          '获取天气预报失败: $e',
        );
      }
    },
  );

  // 注册城市信息资源
  server.resource(
    'supported_cities',
    'weather://cities',
    (uri, extra) async {
      const cities = [
        'Beijing', 'Shanghai', 'Guangzhou', 'Shenzhen',
        'New York', 'London', 'Tokyo', 'Paris',
      ];
      
      return ReadResourceResult(
        contents: [
          TextResourceContents(
            uri: uri.toString(),
            text: cities.join('\n'),
            mimeType: 'text/plain',
          ),
        ],
      );
    },
    description: '支持的城市列表',
    mimeType: 'text/plain',
  );

  // 启动服务器
  await server.connect(StdioServerTransport());
  print('🌤️ 天气服务器已启动');
}
```

#### 文件管理客户端

```dart
// file_manager_client.dart
import 'dart:io';
import 'package:mcp_dart/mcp_dart.dart';

class FileManagerClient {
  late Client _client;
  
  Future<void> initialize() async {
    _client = Client(
      Implementation(name: "file-manager", version: "1.0.0"),
      options: ClientOptions(
        capabilities: ClientCapabilities(
          roots: ClientCapabilitiesRoots(listChanged: true),
        ),
      ),
    );
    
    final transport = StdioClientTransport(
      StdioServerParameters(
        command: 'dart',
        args: ['run', 'file_server.dart'],
      ),
    );
    
    await _client.connect(transport);
    print('✅ 已连接到文件服务器');
  }
  
  Future<void> listFiles(String directory) async {
    try {
      final result = await _client.callTool(
        CallToolRequestParams(
          name: 'list_files',
          arguments: {'path': directory},
        ),
      );
      
      for (final content in result.content) {
        if (content is TextContent) {
          print('📁 $directory 目录内容：');
          print(content.text);
        }
      }
    } catch (e) {
      print('❌ 列出文件失败: $e');
    }
  }
  
  Future<void> readFile(String filePath) async {
    try {
      final result = await _client.callTool(
        CallToolRequestParams(
          name: 'read_file',
          arguments: {'path': filePath},
        ),
      );
      
      for (final content in result.content) {
        if (content is TextContent) {
          print('📄 文件内容 ($filePath)：');
          print(content.text);
        }
      }
    } catch (e) {
      print('❌ 读取文件失败: $e');
    }
  }
  
  Future<void> writeFile(String filePath, String content) async {
    try {
      final result = await _client.callTool(
        CallToolRequestParams(
          name: 'write_file',
          arguments: {
            'path': filePath,
            'content': content,
          },
        ),
      );
      
      print('✅ 文件写入成功');
    } catch (e) {
      print('❌ 写入文件失败: $e');
    }
  }
  
  Future<void> dispose() async {
    await _client.close();
  }
}

void main() async {
  final client = FileManagerClient();
  
  try {
    await client.initialize();
    
    // 列出当前目录文件
    await client.listFiles('.');
    
    // 读取特定文件
    await client.readFile('pubspec.yaml');
    
    // 写入新文件
    await client.writeFile(
      'test_output.txt',
      'Hello from MCP Dart!\nGenerated at: ${DateTime.now()}',
    );
    
  } finally {
    await client.dispose();
  }
}
```

### 🌐 Web应用示例

#### SSE Web服务器

```dart
// web_server.dart
import 'dart:io';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_static/shelf_static.dart';
import 'package:mcp_dart/mcp_dart.dart';

void main() async {
  // 创建MCP服务器
  final mcpServer = McpServer(
    Implementation(name: "web-api-server", version: "1.0.0"),
    options: ServerOptions(
      capabilities: ServerCapabilities(
        tools: ServerCapabilitiesTools(),
        logging: ServerCapabilitiesLogging(),
      ),
    ),
  );

  // 注册API工具
  mcpServer.tool(
    'get_server_info',
    description: '获取服务器信息',
    callback: ({args, extra}) async {
      return CallToolResult.fromContent(
        content: [
          TextContent(
            text: jsonEncode({
              'server': 'MCP Dart Web Server',
              'version': '1.0.0',
              'uptime': '${DateTime.now().difference(startTime).inSeconds}s',
              'memory': '${ProcessInfo.currentRss ~/ 1024 ~/ 1024}MB',
            }),
          ),
        ],
      );
    },
  );

  // 创建SSE管理器
  final sseManager = SseServerManager(mcpServer);
  
  // 设置HTTP路由
  final handler = Pipeline()
      .addMiddleware(logRequests())
      .addMiddleware(_corsMiddleware())
      .addHandler(_createHandler(sseManager));

  // 启动HTTP服务器
  final server = await serve(handler, InternetAddress.anyIPv4, 8080);
  print('🌐 Web服务器运行在 http://localhost:8080');
}

Handler _createHandler(SseServerManager sseManager) {
  return (Request request) async {
    final path = request.url.path;
    
    switch (path) {
      case 'sse':
        return await sseManager.handleSseRequest(request);
        
      case 'message':
        if (request.method == 'POST') {
          return await sseManager.handleMessageRequest(request);
        }
        break;
        
      case 'status':
        return Response.ok(
          jsonEncode({'status': 'running', 'timestamp': DateTime.now().toIso8601String()}),
          headers: {'content-type': 'application/json'},
        );
    }
    
    // 静态文件服务
    return await createStaticHandler('web')((request));
  };
}

Middleware _corsMiddleware() {
  return createMiddleware(
    responseHandler: (Response response) {
      return response.change(headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      });
    },
  );
}
```

---

## 最佳实践

### 🏗️ 项目结构建议

```
my_mcp_project/
├── lib/
│   ├── src/
│   │   ├── tools/           # 工具实现
│   │   │   ├── weather_tool.dart
│   │   │   └── file_tool.dart
│   │   ├── resources/       # 资源处理
│   │   │   ├── config_resource.dart
│   │   │   └── data_resource.dart
│   │   ├── services/        # 业务服务
│   │   │   ├── api_service.dart
│   │   │   └── database_service.dart
│   │   └── utils/           # 工具函数
│   │       ├── validation.dart
│   │       └── logging.dart
│   └── mcp_server.dart      # 主服务器文件
├── bin/
│   └── server.dart         # 可执行入口
├── test/
│   ├── tools_test.dart
│   └── integration_test.dart
├── config/
│   └── server_config.yaml
└── pubspec.yaml
```

### 📝 代码组织模式

#### 1. 工具模块化

```dart
// lib/src/tools/base_tool.dart
abstract class BaseTool {
  String get name;
  String get description;
  Map<String, dynamic>? get inputSchemaProperties;
  List<String>? get inputSchemaRequired;
  
  Future<CallToolResult> execute(Map<String, dynamic>? args, CallToolExtra? extra);
  
  void register(McpServer server) {
    server.tool(
      name,
      description: description,
      inputSchemaProperties: inputSchemaProperties,
      inputSchemaRequired: inputSchemaRequired,
      callback: ({args, extra}) => execute(args, extra),
    );
  }
}

// lib/src/tools/calculator_tool.dart
class CalculatorTool extends BaseTool {
  @override
  String get name => 'calculate';

  @override
  String get description => '执行数学计算';

  @override
  Map<String, dynamic>? get inputSchemaProperties => {
    'expression': {
      'type': 'string',
      'description': '数学表达式',
    },
  };

  @override
  List<String>? get inputSchemaRequired => ['expression'];

  @override
  Future<CallToolResult> execute(Map<String, dynamic>? args, CallToolExtra? extra) async {
    final expression = args!['expression'] as String;
    
    try {
      final result = _evaluateExpression(expression);
      return CallToolResult.fromText('结果: $result');
    } catch (e) {
      throw McpError(
        ErrorCode.invalidParams.value,
        '无效的数学表达式: $expression',
      );
    }
  }
  
  double _evaluateExpression(String expression) {
    // 实现表达式计算逻辑
    return 0.0;
  }
}
```

#### 2. 配置管理

```dart
// lib/src/config/server_config.dart
class ServerConfig {
  final String name;
  final String version;
  final List<String> enabledTools;
  final Map<String, dynamic> toolConfigs;
  final LogLevel logLevel;
  
  ServerConfig({
    required this.name,
    required this.version,
    required this.enabledTools,
    required this.toolConfigs,
    required this.logLevel,
  });
  
  static ServerConfig fromFile(String path) {
    final file = File(path);
    final yaml = loadYaml(file.readAsStringSync());
    
    return ServerConfig(
      name: yaml['name'],
      version: yaml['version'],
      enabledTools: List<String>.from(yaml['enabled_tools'] ?? []),
      toolConfigs: Map<String, dynamic>.from(yaml['tool_configs'] ?? {}),
      logLevel: LogLevel.values.byName(yaml['log_level'] ?? 'info'),
    );
  }
}
```

#### 3. 错误处理策略

```dart
// lib/src/utils/error_handler.dart
class ErrorHandler {
  static Future<T> withErrorHandling<T>(
    Future<T> Function() operation,
    String context,
  ) async {
    try {
      return await operation();
    } on ValidationException catch (e) {
      throw McpError(
        ErrorCode.invalidParams.value,
        '$context: 参数验证失败',
        {'field': e.field, 'message': e.message},
      );
    } on TimeoutException catch (e) {
      throw McpError(
        ErrorCode.requestTimeout.value,
        '$context: 操作超时',
      );
    } on SocketException catch (e) {
      throw McpError(
        ErrorCode.connectionClosed.value,
        '$context: 网络连接错误',
        {'details': e.message},
      );
    } catch (e) {
      // 记录未知错误
      logger.error('Unexpected error in $context', error: e);
      
      throw McpError(
        ErrorCode.internalError.value,
        '$context: 内部服务器错误',
      );
    }
  }
}
```

### 🧪 测试策略

#### 1. 单元测试

```dart
// test/tools/calculator_tool_test.dart
import 'package:test/test.dart';
import 'package:mcp_dart/mcp_dart.dart';
import '../../lib/src/tools/calculator_tool.dart';

void main() {
  group('CalculatorTool', () {
    late CalculatorTool tool;
    
    setUp(() {
      tool = CalculatorTool();
    });
    
    test('should calculate simple expressions', () async {
      final result = await tool.execute(
        {'expression': '2 + 3'},
        null,
      );
      
      expect(result.content.first, isA<TextContent>());
      final content = result.content.first as TextContent;
      expect(content.text, contains('5'));
    });
    
    test('should handle invalid expressions', () async {
      expect(
        () => tool.execute({'expression': 'invalid'}, null),
        throwsA(isA<McpError>()),
      );
    });
  });
}
```

#### 2. 集成测试

```dart
// test/integration_test.dart
import 'package:test/test.dart';
import 'package:mcp_dart/mcp_dart.dart';

void main() {
  group('MCP Integration Tests', () {
    late McpServer server;
    late Client client;
    
    setUp(() async {
      // 创建测试用的内存传输
      final inputController = StreamController<List<int>>();
      final outputController = StreamController<List<int>>();
      
      final serverTransport = IOStreamTransport(
        stream: inputController.stream,
        sink: outputController.sink,
      );
      
      final clientTransport = IOStreamTransport(
        stream: outputController.stream,
        sink: inputController.sink,
      );
      
      // 设置服务器
      server = McpServer(Implementation(name: "test-server", version: "1.0.0"));
      server.tool('echo', callback: ({args, extra}) async {
        final message = args?['message'] ?? 'Hello';
        return CallToolResult.fromText(message);
      });
      
      // 设置客户端
      client = Client(Implementation(name: "test-client", version: "1.0.0"));
      
      // 建立连接
      await server.connect(serverTransport);
      await client.connect(clientTransport);
    });
    
    tearDown(() async {
      await client.close();
      await server.close();
    });
    
    test('should handle tool calls', () async {
      final result = await client.callTool(
        CallToolRequestParams(
          name: 'echo',
          arguments: {'message': 'Test message'},
        ),
      );
      
      expect(result.content.first, isA<TextContent>());
      final content = result.content.first as TextContent;
      expect(content.text, equals('Test message'));
    });
  });
}
```

### 🔒 安全考虑

#### 1. 参数验证

```dart
class InputValidator {
  static void validateString(String? value, String fieldName, {int? maxLength}) {
    if (value == null || value.isEmpty) {
      throw ValidationException(fieldName, '不能为空');
    }
    
    if (maxLength != null && value.length > maxLength) {
      throw ValidationException(fieldName, '长度不能超过$maxLength字符');
    }
  }
  
  static void validateFilePath(String path) {
    // 防止路径遍历攻击
    if (path.contains('..') || path.startsWith('/')) {
      throw ValidationException('path', '不安全的文件路径');
    }
  }
  
  static void validateEmail(String email) {
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(email)) {
      throw ValidationException('email', '无效的邮箱格式');
    }
  }
}
```

#### 2. 权限控制

```dart
class PermissionChecker {
  static final Map<String, Set<String>> _rolePermissions = {
    'admin': {'read', 'write', 'delete', 'manage'},
    'user': {'read', 'write'},
    'guest': {'read'},
  };
  
  static bool hasPermission(String role, String action) {
    return _rolePermissions[role]?.contains(action) ?? false;
  }
  
  static void requirePermission(String role, String action) {
    if (!hasPermission(role, action)) {
      throw McpError(
        ErrorCode.unauthorizedAccess.value,
        '权限不足：需要$action权限',
      );
    }
  }
}
```

### ⚡ 性能优化

#### 1. 资源池化

```dart
class ConnectionPool<T> {
  final List<T> _available = [];
  final Set<T> _inUse = {};
  final T Function() _factory;
  final int maxSize;
  
  ConnectionPool(this._factory, {this.maxSize = 10});
  
  T acquire() {
    if (_available.isNotEmpty) {
      final connection = _available.removeLast();
      _inUse.add(connection);
      return connection;
    }
    
    if (_inUse.length < maxSize) {
      final connection = _factory();
      _inUse.add(connection);
      return connection;
    }
    
    throw StateError('连接池已满');
  }
  
  void release(T connection) {
    if (_inUse.remove(connection)) {
      _available.add(connection);
    }
  }
}
```

#### 2. 缓存策略

```dart
class LRUCache<K, V> {
  final int maxSize;
  final Map<K, V> _cache = {};
  final List<K> _accessOrder = [];
  
  LRUCache(this.maxSize);
  
  V? get(K key) {
    final value = _cache[key];
    if (value != null) {
      _accessOrder.remove(key);
      _accessOrder.add(key);
    }
    return value;
  }
  
  void put(K key, V value) {
    if (_cache.containsKey(key)) {
      _accessOrder.remove(key);
    } else if (_cache.length >= maxSize) {
      final oldest = _accessOrder.removeAt(0);
      _cache.remove(oldest);
    }
    
    _cache[key] = value;
    _accessOrder.add(key);
  }
}
```

### 📊 监控和调试

#### 1. 性能监控

```dart
class PerformanceMonitor {
  static final Map<String, List<Duration>> _metrics = {};
  
  static Future<T> measure<T>(String operation, Future<T> Function() task) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await task();
      stopwatch.stop();
      
      _metrics.putIfAbsent(operation, () => []).add(stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      _metrics.putIfAbsent('$operation.error', () => []).add(stopwatch.elapsed);
      rethrow;
    }
  }
  
  static Map<String, dynamic> getStats() {
    final stats = <String, dynamic>{};
    
    for (final entry in _metrics.entries) {
      final durations = entry.value;
      final avg = durations.map((d) => d.inMilliseconds).reduce((a, b) => a + b) / durations.length;
      
      stats[entry.key] = {
        'count': durations.length,
        'avg_ms': avg.round(),
        'min_ms': durations.map((d) => d.inMilliseconds).reduce(math.min),
        'max_ms': durations.map((d) => d.inMilliseconds).reduce(math.max),
      };
    }
    
    return stats;
  }
}
```

#### 2. 调试工具

```dart
class DebugHelper {
  static bool get isDebugMode => bool.fromEnvironment('DEBUG', defaultValue: false);
  
  static void debugLog(String message, {Object? data}) {
    if (isDebugMode) {
      print('[DEBUG] $message');
      if (data != null) {
        print('[DEBUG] Data: ${jsonEncode(data)}');
      }
    }
  }
  
  static void traceCall(String methodName, Map<String, dynamic>? params) {
    if (isDebugMode) {
      print('[TRACE] $methodName(${params ?? {}})');
    }
  }
}
``` 